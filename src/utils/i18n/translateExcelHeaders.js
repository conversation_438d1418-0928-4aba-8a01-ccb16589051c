const path = require('path');
const xlsx = require('xlsx');
const fs = require('fs');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const translationUtils = require('./translationUtils');

// 多语言翻译模版表头工具

// 解析命令行参数
const argv = yargs(hideBin(process.argv))
  .option('files', {
    alias: 'f',
    description: '要翻译的Excel文件路径，可指定多个',
    type: 'array',
    demandOption: true,
  })
  .option('outputDir', {
    alias: 'o',
    description: '输出目录路径',
    type: 'string',
    default: '',
  })
  .option('languages', {
    alias: 'l',
    description: '目标语言代码列表',
    type: 'array',
    default: translationUtils.DEFAULT_LANGUAGES,
  })
  .option('apiKey', {
    alias: 'k',
    description: '豆包API密钥',
    type: 'string',
    default: process.env.DOUBAN_API_KEY || translationUtils.DEFAULT_API_KEY,
  })
  .option('batchSize', {
    alias: 'b',
    description: '每批翻译的表头数量',
    type: 'number',
    default: 10,
  })
  .option('timeout', {
    alias: 't',
    description: 'API请求超时时间(毫秒)',
    type: 'number',
    default: translationUtils.DEFAULT_TIMEOUT,
  })
  .example('node translateExcelHeaders.js -f path/to/file1.xlsx path/to/file2.xlsx', '翻译多个Excel文件')
  .example('node translateExcelHeaders.js -f file.xlsx -o output/', '指定输出目录')
  .example('node translateExcelHeaders.js -f file.xlsx -l en-US fr-FR', '只翻译为英语和法语')
  .wrap(120)
  .help().argv;

// 设置语言列表
const exportLanguages = argv.languages;
const DOUBAN_API_KEY = argv.apiKey;
const BATCH_SIZE = argv.batchSize;
const REQUEST_TIMEOUT = argv.timeout;

// 确认输出目录
const outputDirectory = argv.outputDir ? path.resolve(argv.outputDir) : '';
if (outputDirectory && !fs.existsSync(outputDirectory)) {
  fs.mkdirSync(outputDirectory, { recursive: true });
  console.log(`已创建输出目录: ${outputDirectory}`);
}

// 翻译缓存，用于减少重复翻译
const translationCache = new Map();

/**
 * 使用豆包大模型进行表头的多语言翻译（一次翻译多种语言）
 * @param {Array<string>} headers 要翻译的表头文本数组
 * @returns {Promise<Object>} 包含所有目标语言翻译结果的对象
 */
async function translateHeadersToMultipleLanguages(headers) {
  // 筛选需要翻译的文本，移除已缓存的
  const textsToTranslate = [];
  const cacheIndices = [];

  // 收集需要翻译的文本和它们的索引
  for (let i = 0; i < headers.length; i++) {
    const text = headers[i];
    let allCached = true;
    // 检查每种语言是否都已经缓存
    for (const lang of exportLanguages) {
      // 中文不需要翻译，直接使用原文缓存
      if (lang === 'zh-CN') {
        translationCache.set(`${text}|${lang}`, text);
        continue;
      }
      const cacheKey = `${text}|${lang}`;
      if (!translationCache.has(cacheKey)) {
        allCached = false;
        break;
      }
    }

    if (!allCached) {
      textsToTranslate.push(text);
      cacheIndices.push(i);
    }
  }

  // 如果所有文本都已缓存，直接返回缓存结果
  if (textsToTranslate.length === 0) {
    console.log('所有表头已缓存，跳过API调用');

    // 从缓存中提取所有翻译
    const result = {};
    for (const lang of exportLanguages) {
      result[lang] = headers.map((text) => translationCache.get(`${text}|${lang}`) || `[${lang}] ${text}`);
    }
    return result;
  }

  console.log(`将${textsToTranslate.length}个表头分成批次进行翻译，每批最多${BATCH_SIZE}个`);

  // 过滤掉中文，不需要翻译
  const languagesToTranslate = exportLanguages.filter((lang) => lang !== 'zh-CN');

  // 如果只有中文，则不需要进行API调用
  if (languagesToTranslate.length === 0) {
    console.log('目标语言只有中文，无需翻译');
    // 直接返回中文结果
    const result = {};
    for (const lang of exportLanguages) {
      result[lang] = headers;
    }
    return result;
  }

  // 准备存储所有批次的翻译结果
  const allTranslations = {};
  // 为每种语言初始化结果数组
  for (const lang of exportLanguages) {
    // 中文直接使用原文
    if (lang === 'zh-CN') {
      allTranslations[lang] = [...headers];
      continue;
    }
    // 其他语言初始化为与headers相同长度的空数组
    allTranslations[lang] = Array(headers.length).fill('');
  }

  // 使用通用批处理函数处理翻译
  try {
    // 将待翻译的文本分成小批次
    const batches = [];
    for (let i = 0; i < textsToTranslate.length; i += BATCH_SIZE) {
      batches.push({
        texts: textsToTranslate.slice(i, i + BATCH_SIZE),
        indices: cacheIndices.slice(i, i + BATCH_SIZE),
      });
    }

    // 逐批处理翻译
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(`正在处理第${batchIndex + 1}/${batches.length}批，包含${batch.texts.length}个表头`);

      // 调用通用翻译函数
      const result = await translationUtils.translateBatch({
        texts: batch.texts,
        targetLanguages: languagesToTranslate,
        apiKey: DOUBAN_API_KEY,
        timeout: REQUEST_TIMEOUT,
        promptType: 'headers',
      });

      // 处理翻译结果
      if (result.translations) {
        for (const lang of languagesToTranslate) {
          if (result.translations[lang] && Array.isArray(result.translations[lang])) {
            // 将每个翻译结果放入对应的位置
            for (let i = 0; i < Math.min(batch.texts.length, result.translations[lang].length); i++) {
              const originalIndex = batch.indices[i];
              const originalText = headers[originalIndex];
              const translatedText = result.translations[lang][i];

              // 更新总结果数组
              allTranslations[lang][originalIndex] = translatedText;

              // 存入缓存
              translationCache.set(`${originalText}|${lang}`, translatedText);
            }
          }
        }
      }

      // 在批次之间添加延迟，避免频繁请求
      if (batchIndex < batches.length - 1) {
        const delayTime = 100; // 100毫秒延迟
        console.log(`等待${delayTime}毫秒后处理下一批...`);
        await new Promise((resolve) => setTimeout(resolve, delayTime));
      }
    }
  } catch (error) {
    // 处理翻译错误
    console.error(`翻译失败: ${error.message}`);
    throw error;
  }

  return allTranslations;
}

/**
 * 处理Excel文件
 * @param {string} filePath 文件路径
 */
async function processExcelFile(filePath) {
  try {
    // 读取Excel文件
    const workbook = xlsx.readFile(filePath);
    const fileName = path.basename(filePath, '.xlsx');

    // 遍历每个工作表
    for (const sheetName of workbook.SheetNames) {
      const worksheet = workbook.Sheets[sheetName];

      // 防止空工作表引起的错误
      if (!worksheet || !worksheet['!ref']) {
        console.log(`工作表 ${sheetName} 为空或没有内容范围，跳过处理`);
        continue;
      }

      // 获取表头范围
      const range = xlsx.utils.decode_range(worksheet['!ref']);

      // 收集表头
      const headerCells = [];
      const headerTexts = [];

      // 提取表头（第一行）
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = xlsx.utils.encode_cell({ r: 0, c: col });
        const cell = worksheet[cellAddress];
        if (cell && cell.v) {
          const headerText = cell.v.toString();
          headerCells.push({
            col,
            address: cellAddress,
            cell,
          });
          headerTexts.push(headerText);
        }
      }

      console.log(`从工作表 ${sheetName} 中提取了 ${headerTexts.length} 个表头`);
      console.log('提取的表头:', JSON.stringify(headerTexts, null, 2));

      // 一次请求多语言翻译，减少API调用
      let translationResults = {};
      if (headerTexts.length > 0) {
        translationResults = await translateHeadersToMultipleLanguages(headerTexts);

        // 添加此行显示翻译结果
        console.log(
          '最终翻译结果示例:',
          JSON.stringify(
            Object.keys(translationResults).reduce((acc, lang) => {
              acc[lang] = translationResults[lang].slice(0, 3); // 只显示前三个结果
              return acc;
            }, {})
          )
        );
      } else {
        console.log('没有找到表头，跳过翻译');
        // 创建空的翻译结果
        translationResults = exportLanguages.reduce((acc, lang) => {
          acc[lang] = [];
          return acc;
        }, {});
      }

      // 为每种语言创建新的Excel文件
      for (const lang of exportLanguages) {
        // 创建新的工作簿
        const newWorkbook = xlsx.utils.book_new();
        // 复制原始工作表
        const newWorksheet = xlsx.utils.aoa_to_sheet([]);
        xlsx.utils.book_append_sheet(newWorkbook, newWorksheet, sheetName);

        // 设置与原工作表相同的列宽
        if (worksheet['!cols']) {
          newWorksheet['!cols'] = [...worksheet['!cols']];
        }

        // 设置与原工作表相同的行高
        if (worksheet['!rows']) {
          newWorksheet['!rows'] = [...worksheet['!rows']];
        }

        // 复制所有单元格数据
        for (let r = range.s.r; r <= range.e.r; r++) {
          for (let c = range.s.c; c <= range.e.c; c++) {
            const cellAddress = xlsx.utils.encode_cell({ r, c });
            const cell = worksheet[cellAddress];

            if (cell) {
              if (r === 0 && cell.v) {
                // 处理表头行
                // 创建新的单元格
                const newCell = { ...cell };

                // 查找该单元格在headerCells中的索引
                const headerIndex = headerCells.findIndex((h) => h.address === cellAddress);
                if (headerIndex !== -1 && translationResults[lang] && translationResults[lang][headerIndex]) {
                  newCell.v = translationResults[lang][headerIndex];
                }

                // 设置单元格
                newWorksheet[cellAddress] = newCell;
              } else {
                // 非表头行，直接复制
                newWorksheet[cellAddress] = { ...cell };
              }
            }
          }
        }

        // 设置工作表范围
        newWorksheet['!ref'] = worksheet['!ref'];

        // 生成新的文件名
        const langCode = lang.replace('-', '_');
        const newFileName = `${fileName}_${langCode}.xlsx`;

        // 确定输出路径
        let newFilePath;
        if (outputDirectory) {
          newFilePath = path.join(outputDirectory, newFileName);
        } else {
          newFilePath = path.join(path.dirname(filePath), newFileName);
        }

        // 写入新文件
        xlsx.writeFile(newWorkbook, newFilePath);
        console.log(`已创建 ${newFilePath}`);
      }
    }

    console.log(`文件 ${filePath} 处理完成`);
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error);
    process.exit(1);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('开始处理Excel文件...');
  console.log(`翻译目标语言: ${exportLanguages.join(', ')}`);
  console.log(`使用豆包AI进行表头翻译，已启用翻译缓存`);

  // 检查文件是否存在
  const validFiles = [];
  for (const filePath of argv.files) {
    const resolvedPath = path.resolve(filePath);
    if (fs.existsSync(resolvedPath)) {
      validFiles.push(resolvedPath);
    } else {
      console.error(`错误: 文件不存在 - ${resolvedPath}`);
    }
  }

  if (validFiles.length === 0) {
    console.error('错误: 没有有效的Excel文件可处理');
    process.exit(1);
  }

  // 重置API调用计数器
  translationUtils.resetApiCallCount();

  for (const filePath of validFiles) {
    console.log(`处理文件: ${filePath}`);
    await processExcelFile(filePath);
  }

  console.log(`所有文件处理完成! 总共调用豆包API ${translationUtils.getApiCallCount()} 次`);
}

// 执行主函数
main().catch((error) => {
  console.error('程序执行出错:', error);
  process.exit(1);
});
