/**
 * 翻译工具模块 - 为多语言翻译功能提供通用工具和API调用函数
 */

const axios = require('axios');

// 豆包大模型API配置
const DOUBAN_API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
// 默认的API密钥（建议通过环境变量覆盖）
const DEFAULT_API_KEY = '70d2a441-2b18-425f-8c44-fe072511af7e';

// 默认支持的语言列表
const DEFAULT_LANGUAGES = ['zh-CN', 'en-US', 'es-ES', 'pt-PT', 'ar-AR', 'th-TH'];

// 语言名称映射
const LANGUAGE_NAMES = {
  'zh-CN': '中文',
  'en-US': '英语',
  'es-ES': '西班牙语',
  'pt-PT': '葡萄牙语',
  'ar-AR': '阿拉伯语',
  'th-TH': '泰语',
  'fr-FR': '法语',
  'de-DE': '德语',
  'it-IT': '意大利语',
  'ja-JP': '日语',
  'ko-KR': '韩语',
  'ru-RU': '俄语',
  'vi-VN': '越南语',
  'id-ID': '印尼语',
  'ms-MY': '马来语',
  'tr-TR': '土耳其语',
};

// 默认超时设置（毫秒）
const DEFAULT_TIMEOUT = 60000; // 60秒

// 跟踪API调用次数
let apiCallCount = 0;

/**
 * 重置API调用计数器
 */
function resetApiCallCount() {
  apiCallCount = 0;
}

/**
 * 获取当前API调用次数
 * @returns {number} API调用次数
 */
function getApiCallCount() {
  return apiCallCount;
}

/**
 * 调用豆包API进行批次翻译
 * @param {Object} options 配置选项
 * @param {Array} options.texts 待翻译的文本数组
 * @param {Array} options.targetLanguages 目标语言代码数组
 * @param {string} options.apiKey API密钥
 * @param {number} options.timeout 超时时间（毫秒）
 * @param {boolean} options.verbose 是否输出详细日志
 * @param {string} options.promptType 提示词类型，可选值: 'headers'(表头), 'texts'(一般文本)
 * @returns {Promise<Object>} 翻译结果
 */
async function translateBatch({
  texts,
  targetLanguages = DEFAULT_LANGUAGES.filter((lang) => lang !== 'zh-CN'),
  apiKey = process.env.DOUBAN_API_KEY || DEFAULT_API_KEY,
  timeout = DEFAULT_TIMEOUT,
  verbose = true,
  promptType = 'texts',
}) {
  if (!texts || texts.length === 0) {
    return {};
  }

  try {
    // 增加API调用计数
    apiCallCount++;
    if (verbose) {
      console.log(`【API调用统计】当前是第 ${apiCallCount} 次调用豆包API，本次翻译 ${texts.length} 个项目`);
    }

    // 构建语言列表字符串
    const languageList = targetLanguages.map((lang) => `${LANGUAGE_NAMES[lang] || lang} (${lang})`).join(', ');

    let prompt;
    let expectedResponseFormat;

    // 根据promptType构建不同的提示词
    if (promptType === 'headers') {
      // 表头翻译格式
      const textsFormatted = texts.map((text, index) => `表头${index + 1}: "${text}"`).join('\n');
      expectedResponseFormat = `{
  "translations": {
    ${targetLanguages.map((lang) => `"${lang}": ["${lang}翻译1", "${lang}翻译2", ...]`).join(',\n    ')}
  }
}`;

      prompt = `请将以下中文表头翻译成多种语言，并以JSON格式返回。请保持专业性，确保翻译准确符合表格上下文。
需要翻译的语言有: ${languageList}

返回格式要求:
${expectedResponseFormat}

请只返回JSON格式的结果，不要有任何解释或额外内容。

要翻译的表头有:
${textsFormatted}`;
    } else {
      // 一般文本翻译格式
      const textsFormatted = texts.map((text, index) => `项目${index + 1}: "${text}"`).join('\n');
      expectedResponseFormat = `{
  "items": [
    {
      "index": 0,
      "translations": {
        ${targetLanguages.map((lang) => `"${lang}": "${lang}翻译1"`).join(',\n        ')}
      }
    },
    {
      "index": 1,
      "translations": { ... }
    },
    ...
  ]
}`;

      prompt = `请将以下多个中文文本翻译成多种语言，并以JSON格式返回。
需要翻译的语言有: ${languageList}

返回格式要求:
${expectedResponseFormat}

请只返回JSON格式的结果，不要有任何解释或额外内容。

要翻译的文本有:
${textsFormatted}`;
    }

    if (verbose) {
      console.log(`正在请求将 ${texts.length} 个项目翻译为 ${targetLanguages.length} 种语言...`);
    }

    // 调用豆包API
    const response = await axios.post(
      DOUBAN_API_URL,
      {
        model: 'doubao-1-5-pro-32k-250115',
        messages: [
          { role: 'system', content: '你是专业的多语言翻译专家。请提供准确的翻译，并以指定的JSON格式返回结果。' },
          { role: 'user', content: prompt },
        ],
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiKey}`,
        },
        timeout: timeout,
      }
    );

    if (response.data?.choices?.[0]?.message?.content) {
      const content = response.data.choices[0].message.content.trim();

      if (verbose) {
        console.log('API 返回原始内容(前200字符):', content.substring(0, 200) + '...');
      }

      // 提取JSON内容
      const jsonStr = extractJsonFromString(content);
      if (!jsonStr) {
        throw new Error('翻译结果中没有找到有效的JSON格式');
      }

      // 解析JSON
      const result = JSON.parse(jsonStr);

      // 根据promptType解析不同的结果格式
      if (promptType === 'headers') {
        // 验证headers格式的结果
        if (!result.translations) {
          throw new Error('翻译结果格式不符合预期，缺少translations对象');
        }
        return result;
      } else {
        // 验证texts格式的结果
        if (!result.items || !Array.isArray(result.items)) {
          throw new Error('翻译结果格式不符合预期，缺少items数组');
        }
        return result;
      }
    }

    // API返回格式异常
    if (verbose) {
      console.warn('豆包AI返回数据异常:', JSON.stringify(response.data));
    }
    throw new Error('翻译结果格式异常');
  } catch (error) {
    // 增强错误信息
    let errorMessage;
    if (axios.isAxiosError(error)) {
      if (error.response) {
        errorMessage = `豆包AI调用失败: HTTP ${error.response.status} - ${JSON.stringify(error.response.data)}`;
      } else if (error.request) {
        if (error.message.includes('timeout')) {
          errorMessage = `豆包API调用超时，请减少单次翻译的数量或增加超时时间`;
        } else {
          errorMessage = `豆包AI请求网络错误: ${error.message}`;
        }
      } else {
        errorMessage = `豆包AI请求配置错误: ${error.message}`;
      }
    } else {
      errorMessage = error instanceof Error ? error.message : String(error);
    }

    if (verbose) {
      console.error('翻译失败:', errorMessage);
    }

    // 重新抛出增强后的错误
    const enhancedError = new Error(errorMessage);
    enhancedError.originalError = error;
    enhancedError.isTimeout = error.message.includes('timeout');
    throw enhancedError;
  }
}

/**
 * 从字符串中提取JSON
 * @param {string} str 包含JSON的字符串
 * @returns {string|null} 提取的JSON字符串，如果未找到则返回null
 */
function extractJsonFromString(str) {
  if (!str) return null;

  const jsonStart = str.indexOf('{');
  const jsonEnd = str.lastIndexOf('}');

  if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
    return str.substring(jsonStart, jsonEnd + 1);
  }

  return null;
}

/**
 * 将多个项目分批处理，进行翻译并合并结果
 * @param {Object} options 配置选项
 * @param {Array} options.items 待翻译的项目数组
 * @param {number} options.batchSize 每批处理的项目数量
 * @param {Function} options.processBatchFn 处理单个批次的函数
 * @param {Function} options.onBatchComplete 批次完成回调
 * @param {number} options.delayBetweenBatches 批次之间的延迟时间(毫秒)
 * @param {number} options.maxRetries 最大重试次数
 * @param {boolean} options.verbose 是否输出详细日志
 * @returns {Promise<Array>} 处理结果数组
 */
async function processBatches({
  items,
  batchSize = 5,
  processBatchFn,
  onBatchComplete = null,
  delayBetweenBatches = 1000,
  maxRetries = 3,
  verbose = true,
}) {
  if (!items || items.length === 0) {
    return [];
  }

  let allResults = [];
  const batchCount = Math.ceil(items.length / batchSize);

  // 将项目分成多个批次处理
  for (let i = 0; i < items.length; i += batchSize) {
    const batchIndex = Math.floor(i / batchSize);
    const batch = items.slice(i, i + batchSize);

    if (verbose) {
      console.log(`处理批次 ${batchIndex + 1}/${batchCount}, 包含${batch.length}个项目`);
    }

    // 翻译重试机制
    let retries = maxRetries;
    let batchResults = null;
    let lastError = null;

    while (retries > 0 && batchResults === null) {
      try {
        // 调用批次处理函数
        batchResults = await processBatchFn(batch, batchIndex);

        // 批次处理成功，调用回调
        if (onBatchComplete) {
          onBatchComplete(batchResults, batchIndex, false);
        }
      } catch (error) {
        lastError = error;

        if (verbose) {
          console.error(`批次 ${batchIndex + 1} 处理失败，剩余重试次数: ${retries - 1}，错误信息: ${error.message}`);
        }

        // 检查是否是超时错误，如果是则直接抛出不再重试
        if (error.isTimeout) {
          throw error;
        }

        // 减少重试次数并等待一段时间
        retries--;
        if (retries > 0) {
          // 递增等待时间，避免频繁请求
          const waitTime = (maxRetries - retries) * 2000;
          if (verbose) {
            console.log(`等待 ${waitTime}ms 后重试...`);
          }
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        }
      }
    }

    // 如果重试后仍然失败，则使用缺省处理或抛出错误
    if (batchResults === null) {
      if (onBatchComplete) {
        const fallbackResults = onBatchComplete(null, batchIndex, true);
        if (fallbackResults) {
          batchResults = fallbackResults;
        } else {
          throw lastError || new Error(`批次 ${batchIndex + 1} 处理失败`);
        }
      } else {
        throw lastError || new Error(`批次 ${batchIndex + 1} 处理失败`);
      }
    }

    // 添加批次结果到总结果中
    allResults = allResults.concat(batchResults);

    // 在批次之间添加延迟，避免频繁请求
    if (i + batchSize < items.length) {
      if (verbose) {
        console.log(`等待${delayBetweenBatches}毫秒后处理下一批...`);
      }
      await new Promise((resolve) => setTimeout(resolve, delayBetweenBatches));
    }
  }

  return allResults;
}

module.exports = {
  // 常量导出
  DEFAULT_LANGUAGES,
  LANGUAGE_NAMES,
  DEFAULT_TIMEOUT,
  DEFAULT_API_KEY,

  // API调用相关
  translateBatch,
  resetApiCallCount,
  getApiCallCount,

  // 批处理相关
  processBatches,

  // 辅助函数
  extractJsonFromString,
};
