/**
 * 翻译key并保存到Excel文件
 * 用法: node src/utils/i18n/translateKey.js <json_file_path> [excel_path] [--overwrite] [--resource-type=TYPE]
 * 简便用法：node src/utils/i18n/translateKey.js src/utils/i18n/local_translations.json
 * 例如: node src/utils/i18n/translateKey.js "translations.json" "src/utils/i18n/translations.xlsx"
 *     或: node src/utils/i18n/translateKey.js "translations.json" "src/utils/i18n/translations.xlsx" --overwrite
 *     或: node src/utils/i18n/translateKey.js "translations.json" "src/utils/i18n/translations.xlsx" --resource-type=MENU
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const translationUtils = require('./translationUtils');

// 确保axios库已安装
try {
  require.resolve('axios');
  console.log('axios库已安装');
} catch (e) {
  console.log('正在安装axios库...');
  execSync('npm install axios --no-save', { stdio: 'inherit' });
}

// axios将通过translationUtils使用

// 确保xlsx库已安装
try {
  require.resolve('xlsx');
  console.log('xlsx库已安装');
} catch (e) {
  console.log('正在安装xlsx库...');
  execSync('npm install xlsx --no-save', { stdio: 'inherit' });
}

const XLSX = require('xlsx');

// 所有支持的语言
const LANGUAGES = translationUtils.DEFAULT_LANGUAGES;

// API请求超时设置（毫秒）
const REQUEST_TIMEOUT = translationUtils.DEFAULT_TIMEOUT;

/**
 * 批量使用豆包大模型进行多组文本的多语言翻译
 * @param {Array<{key: string, text: string}>} items 要翻译的多个中文文本项
 * @returns {Promise<Array<{key: string, translations: Object}>>} 包含各项翻译结果的数组
 */
async function batchTranslateMultipleTexts(items) {
  try {
    // 检查API密钥
    const DOUBAN_API_KEY = process.env.DOUBAN_API_KEY || translationUtils.DEFAULT_API_KEY;
    if (!DOUBAN_API_KEY) {
      throw new Error('未设置豆包API密钥，请设置环境变量DOUBAN_API_KEY');
    }

    // 提取需要翻译的文本
    const texts = items.map((item) => item.text);

    // 构建翻译提示词，一次请求所有语言的翻译
    const targetLanguages = LANGUAGES.filter((lang) => lang !== 'zh-CN'); // 排除中文

    // 使用通用翻译函数
    const result = await translationUtils.translateBatch({
      texts,
      targetLanguages,
      apiKey: DOUBAN_API_KEY,
      timeout: REQUEST_TIMEOUT * 5, // 增加超时时间
      promptType: 'texts',
    });

    // 处理翻译结果
    if (!result.items || !Array.isArray(result.items)) {
      throw new Error('翻译结果格式不符合预期，缺少items数组');
    }

    // 将翻译结果映射回原始项目
    return items.map((item, index) => {
      const translationItem = result.items.find((i) => i.index === index) || result.items[index] || { translations: {} };

      // 确保所有语言都有翻译，添加中文翻译
      const translations = { ...translationItem.translations, 'zh-CN': item.text };

      return {
        key: item.key,
        translations,
      };
    });
  } catch (error) {
    console.error('翻译过程中出错:', error instanceof Error ? error.message : String(error));
    throw error;
  }
}

/**
 * 批量处理多组文本的翻译
 * @param {Array<{key: string, text: string}>} items 要翻译的项目列表
 * @returns {Promise<Array<{key: string, translations: Object}>>} 翻译结果
 */
async function batchGetMultipleTranslations(items) {
  if (items.length === 0) {
    return [];
  }

  // 由于API可能有限制，每批次最多处理10个项目
  const BATCH_SIZE = 10;

  // 使用通用批处理函数
  return translationUtils.processBatches({
    items,
    batchSize: BATCH_SIZE,
    processBatchFn: async (batch) => {
      return await batchTranslateMultipleTexts(batch);
    },
    onBatchComplete: (results, batchIndex, isFallback) => {
      if (isFallback) {
        console.warn(`批次 ${batchIndex + 1} 处理失败，使用占位符翻译`);
        // 创建占位符翻译
        return batch.map((item) => {
          const translations = {};
          for (const lang of LANGUAGES) {
            translations[lang] = lang === 'zh-CN' ? item.text : `[${lang}] ${item.text}`;
          }
          return { key: item.key, translations };
        });
      }
      return results;
    },
    delayBetweenBatches: 2000, // 2秒延迟
    maxRetries: 3,
  });
}

/**
 * 读取Excel文件并返回数据
 * @param {string} excelPath Excel文件路径
 * @returns {Array} Excel中的数据
 */
function readExcelFile(excelPath) {
  // 处理路径，确保使用当前工作目录作为基准
  const currentDir = process.cwd();
  let resolvedPath = excelPath;

  if (!path.isAbsolute(excelPath)) {
    resolvedPath = path.resolve(currentDir, excelPath);
  }

  console.log(`尝试读取Excel文件，原始路径: ${excelPath}`);
  console.log(`当前工作目录: ${currentDir}`);
  console.log(`解析后的Excel文件路径: ${resolvedPath}`);

  if (!fs.existsSync(resolvedPath)) {
    console.log(`Excel文件不存在: ${resolvedPath}`);
    return [];
  }
  try {
    const workbook = XLSX.readFile(resolvedPath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);
    console.log(`从Excel文件中读取了${data.length}行数据`);
    return data;
  } catch (error) {
    console.error(`读取Excel文件出错: ${error.message}`);
    return [];
  }
}

/**
 * 检查翻译项是否已存在于Excel数据中
 * @param {Object} item 翻译项
 * @param {string} resourceType 资源类型
 * @param {Array} excelData Excel数据
 * @returns {boolean} 如果所有语言都已翻译则返回true，否则返回false
 */
function isItemFullyTranslatedInExcel(item, resourceType, excelData) {
  for (const lang of LANGUAGES) {
    const exists = excelData.some((row) => row.resourceType === resourceType && row.code === item.key && row.language === lang);
    if (!exists) {
      return false;
    }
  }
  return true;
}

/**
 * 从Excel中获取已有的翻译
 * @param {Object} item 翻译项
 * @param {string} resourceType 资源类型
 * @param {Array} excelData Excel数据
 * @returns {Object} 已有的翻译
 */
function getExistingTranslations(item, resourceType, excelData) {
  const translations = {};
  for (const lang of LANGUAGES) {
    const existingRow = excelData.find((row) => row.resourceType === resourceType && row.code === item.key && row.language === lang);
    if (existingRow) {
      translations[lang] = existingRow.value;
    }
  }
  return translations;
}

/**
 * 检查Excel中项目的中文文本是否与JSON中的不同
 * @param {Object} item 翻译项
 * @param {string} resourceType 资源类型
 * @param {Array} excelData Excel数据
 * @returns {boolean} 如果中文文本不同则返回true，相同则返回false
 */
function hasChineseTextChanged(item, resourceType, excelData) {
  const existingRow = excelData.find((row) => row.resourceType === resourceType && row.code === item.key && row.language === 'zh-CN');

  if (!existingRow) {
    return true; // 如果Excel中没有相应的中文项，则认为需要翻译
  }

  // 比较中文文本是否相同
  return existingRow.value !== item.text;
}

/**
 * 批量处理JSON文件中的翻译
 * @param {string} jsonFilePath JSON文件路径
 * @param {string} excelPath Excel文件路径
 * @param {boolean} overwrite 是否覆盖已有数据，默认false
 * @param {string} defaultResourceType 默认的资源类型，未指定时使用
 * @returns {Promise<void>}
 */
async function processBatchTranslations(jsonFilePath, excelPath, overwrite = false, defaultResourceType = 'UNIFIED_FRONT_END') {
  try {
    // 读取JSON文件
    if (!fs.existsSync(jsonFilePath)) {
      throw new Error(`JSON文件不存在: ${jsonFilePath}`);
    }

    const jsonContent = fs.readFileSync(jsonFilePath, 'utf8');

    // 检查文件内容是否为空
    if (!jsonContent.trim()) {
      throw new Error(`JSON文件内容为空: ${jsonFilePath}`);
    }

    // 检查文件内容是否以 '/' 或者 '//' 开头 (可能是注释)
    if (jsonContent.trim().startsWith('/')) {
      throw new Error(`JSON文件包含JavaScript注释，不是有效的JSON格式。JSON格式不支持注释，请移除注释或使用有效的JSON格式。`);
    }

    // 尝试解析JSON
    let translationItems;
    try {
      translationItems = JSON.parse(jsonContent);

      // 检查解析后的内容是否为null或undefined
      if (translationItems === null || translationItems === undefined) {
        throw new Error('解析结果为空');
      }
    } catch (error) {
      console.error('JSON解析失败，文件内容前30个字符:', jsonContent.substring(0, 30));
      throw new Error(`JSON解析错误: ${error.message}。请确保文件是有效的JSON格式，不包含注释或其他非法字符。`);
    }

    // 验证JSON格式
    let normalizedItems = [];

    if (Array.isArray(translationItems)) {
      // 如果是数组，直接使用
      normalizedItems = translationItems;
    } else if (typeof translationItems === 'object' && translationItems !== null) {
      // 如果是对象，转换为数组
      console.log('检测到JSON为对象格式，正在转换为数组格式...');

      // 打印一下前两个条目，用于调试
      const firstTwoEntries = Object.entries(translationItems).slice(0, 2);
      console.log('JSON前两个条目示例:', JSON.stringify(firstTwoEntries, null, 2));

      normalizedItems = Object.entries(translationItems)
        .map(([key, value]) => {
          // 处理不同的值格式
          if (typeof value === 'string') {
            // 简单的 key: "value" 格式
            return { key, text: value };
          } else if (typeof value === 'object' && value !== null) {
            // 扩展的 key: { text: "value", resourceType: "type" } 格式
            return {
              key,
              text: value.text || value.value || '',
              resourceType: value.resourceType,
            };
          }
          return null;
        })
        .filter((item) => item !== null);
    } else {
      throw new Error(`JSON内容格式错误: 应该是数组或者对象，但实际类型是 ${typeof translationItems}`);
    }

    // 检查数组项格式
    const originalLength = normalizedItems.length;
    translationItems = normalizedItems.filter((item) => {
      // 支持两种格式: {key: "keyName", text: "中文文本"} 或 {code: "keyName", value: "中文文本"}
      return (
        item && ((typeof item.key === 'string' && typeof item.text === 'string') || (typeof item.code === 'string' && typeof item.value === 'string'))
      );
    });

    // 如果过滤后长度不一致，输出提示
    if (originalLength !== translationItems.length) {
      console.warn(`警告: ${originalLength - translationItems.length}个项目因格式不正确被过滤`);
    }

    if (translationItems.length === 0) {
      throw new Error('JSON内容格式错误: 没有找到有效的翻译项。每个翻译项应该包含key/text或code/value字段。');
    }

    console.log(`从JSON文件中读取了${translationItems.length}条待翻译项`);

    // 准备好批量翻译的数据格式
    const itemsToTranslate = translationItems.map((item) => ({
      key: item.key || item.code,
      text: item.text || item.value,
      resourceType: item.resourceType || defaultResourceType,
    }));

    // 打印处理后的数据，检查resourceType是否正确保留
    console.log('处理后的数据(前2条):', JSON.stringify(itemsToTranslate.slice(0, 2), null, 2));

    // 统计各类resourceType的数量
    const resourceTypeCount = {};
    itemsToTranslate.forEach((item) => {
      resourceTypeCount[item.resourceType] = (resourceTypeCount[item.resourceType] || 0) + 1;
    });
    console.log('各resourceType类型统计:', JSON.stringify(resourceTypeCount, null, 2));

    // 优化：如果不覆盖已有翻译，则先读取Excel文件，过滤掉已翻译的项
    let excelData = [];
    let itemsNeedingTranslation = [];
    let itemsAlreadyTranslated = [];

    // 不管是否是覆盖模式，都先读取Excel数据
    if (fs.existsSync(excelPath)) {
      excelData = readExcelFile(excelPath);

      // 在不同模式下过滤需要翻译的项目
      if (overwrite) {
        // 覆盖模式：只对中文文本有变动的项目进行翻译
        console.log('覆盖模式：检查中文文本是否有变动...');
        for (const item of itemsToTranslate) {
          if (hasChineseTextChanged(item, item.resourceType, excelData)) {
            // 中文文本有变动，需要重新翻译
            itemsNeedingTranslation.push(item);
          } else {
            // 中文文本没变动，使用已有翻译
            const existingTranslations = getExistingTranslations(item, item.resourceType, excelData);
            itemsAlreadyTranslated.push({
              key: item.key,
              translations: existingTranslations,
            });
          }
        }
        console.log(`覆盖模式：发现${itemsNeedingTranslation.length}条中文文本有变动，${itemsAlreadyTranslated.length}条无变动`);
      } else {
        // 非覆盖模式：只对尚未全部翻译的项目进行翻译
        for (const item of itemsToTranslate) {
          if (isItemFullyTranslatedInExcel(item, item.resourceType, excelData)) {
            // 如果所有语言都已翻译，则跳过API调用
            const existingTranslations = getExistingTranslations(item, item.resourceType, excelData);
            itemsAlreadyTranslated.push({
              key: item.key,
              translations: existingTranslations,
            });
          } else {
            // 如果有任一语言未翻译，则需要翻译此项
            itemsNeedingTranslation.push(item);
          }
        }
        console.log(`非覆盖模式：已有${itemsAlreadyTranslated.length}条完整翻译，需要新增翻译${itemsNeedingTranslation.length}条`);
      }
    } else {
      // Excel文件不存在，所有项目都需要翻译
      itemsNeedingTranslation = itemsToTranslate;
      console.log(`Excel文件不存在，将对所有${itemsNeedingTranslation.length}条项目进行翻译`);
    }

    // 只对需要翻译的项目调用API
    let batchResults = [];
    if (itemsNeedingTranslation.length > 0) {
      // 重置API调用计数器
      translationUtils.resetApiCallCount();
      console.log(`开始批量翻译${itemsNeedingTranslation.length}条需要翻译的项目...`);
      batchResults = await batchGetMultipleTranslations(itemsNeedingTranslation);
      console.log(`【API调用统计】本次处理共调用豆包API ${translationUtils.getApiCallCount()} 次`);
    } else {
      console.log('没有需要翻译的项目，跳过豆包API调用');
    }

    // 合并已有翻译和新翻译的结果
    const allResults = [...batchResults, ...itemsAlreadyTranslated];

    // 将结果映射回原始项目的顺序
    const orderedResults = itemsToTranslate.map((item) => {
      return allResults.find((result) => result.key === item.key);
    });

    // 准备Excel数据
    let allTranslationEntries = [];

    // 处理每个项目的翻译结果
    for (let i = 0; i < orderedResults.length; i++) {
      const result = orderedResults[i];
      const key = result.key;
      const resourceType = itemsToTranslate[i].resourceType;

      console.log(`处理第 ${i + 1}/${orderedResults.length} 条翻译结果: ${key} (${resourceType})`);

      // 将每个语言的翻译添加到Excel数据中
      for (const lang of LANGUAGES) {
        const translatedText = result.translations[lang] || `[${lang}] 翻译失败`;
        allTranslationEntries.push({
          resourceType,
          code: key,
          value: translatedText,
          language: lang,
        });
      }
    }

    // 保存到Excel
    try {
      let workbook;
      let data = excelData.length > 0 ? excelData : [];

      // 检查文件是否存在
      if (fs.existsSync(excelPath) && excelData.length === 0) {
        console.log(`读取现有Excel文件: ${excelPath}`);
        workbook = XLSX.readFile(excelPath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        data = XLSX.utils.sheet_to_json(worksheet);
        console.log(`读取了${data.length}行数据`);
      } else if (!fs.existsSync(excelPath)) {
        console.log(`创建新的Excel文件: ${excelPath}`);
        workbook = XLSX.utils.book_new();
      } else {
        console.log(`使用已读取的Excel数据，共${data.length}行`);
        workbook = XLSX.readFile(excelPath);
      }

      // 合并数据
      let updatedCount = 0;
      let addedCount = 0;
      let resourceTypeStats = {};

      allTranslationEntries.forEach((translation) => {
        // 统计resourceType
        resourceTypeStats[translation.resourceType] = (resourceTypeStats[translation.resourceType] || 0) + 1;

        const existingIndex = data.findIndex(
          (row) => row['resourceType'] === translation.resourceType && row['code'] === translation.code && row['language'] === translation.language
        );

        if (existingIndex >= 0) {
          // 如果已存在且允许覆盖，则更新翻译
          if (overwrite) {
            data[existingIndex]['value'] = translation.value;
            updatedCount++;
          } else {
            console.log(`跳过已存在的翻译: ${translation.code} - ${translation.language}`);
          }
        } else {
          // 添加新翻译
          data.push(translation);
          addedCount++;
        }
      });

      console.log(`更新了${updatedCount}条记录，新增了${addedCount}条记录`);
      console.log('最终Excel数据的resourceType统计:', JSON.stringify(resourceTypeStats, null, 2));

      // 保存工作表
      const worksheet = XLSX.utils.json_to_sheet(data);
      if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
      } else {
        workbook.Sheets[workbook.SheetNames[0]] = worksheet;
      }

      XLSX.writeFile(workbook, excelPath);
      console.log(`翻译已成功保存到: ${excelPath}`);
      // 清理可能的临时文件
      const dirPath = path.dirname(excelPath);
      const baseName = path.basename(excelPath);
      const tempFilePath = path.join(dirPath, `.~${baseName}`);
      if (fs.existsSync(tempFilePath)) {
        try {
          fs.unlinkSync(tempFilePath);
          console.log(`已清理临时文件: ${tempFilePath}`);
        } catch (error) {
          console.warn(`清理临时文件失败: ${error.message}`);
        }
      }
    } catch (error) {
      console.error(`保存Excel文件过程中出错: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }

    console.log(`所有翻译项处理完成，共 ${translationItems.length} 条`);
  } catch (error) {
    console.error(`批量处理过程中出错: ${error.message}`);
    throw error;
  }
}

/**
 * 从命令行参数中提取资源类型
 * @param {string[]} args 命令行参数
 * @returns {string|null} 资源类型，如果未找到则返回null
 */
function extractResourceTypeFromArgs(args) {
  const resourceTypeArg = args.find((arg) => arg.startsWith('--resource-type='));
  if (resourceTypeArg) {
    return resourceTypeArg.split('=')[1];
  }
  return null;
}

/**
 * 主函数 - 程序入口
 */
async function main() {
  // 获取命令行参数
  const args = process.argv.slice(2);

  // 检查是否有overwrite参数
  const overwriteIndex = args.indexOf('--overwrite');
  const shouldOverwrite = overwriteIndex !== -1;

  // 提取资源类型参数
  const defaultResourceType = extractResourceTypeFromArgs(args) || 'UNIFIED_FRONT_END';

  // 过滤掉所有选项参数
  const processedArgs = args.filter((arg) => !arg.startsWith('--'));

  // 参数检查
  if (processedArgs.length < 1) {
    console.log('用法: node src/utils/i18n/translateKey.js <json_file_path> [excel_path] [--overwrite] [--resource-type=TYPE]');
    console.log('例如: node src/utils/i18n/translateKey.js "translations.json" "src/utils/i18n/translations.xlsx"');
    console.log('    或: node src/utils/i18n/translateKey.js "translations.json" "src/utils/i18n/translations.xlsx" --overwrite');
    console.log('    或: node src/utils/i18n/translateKey.js "translations.json" "src/utils/i18n/translations.xlsx" --resource-type=MENU');
    console.log('如果不提供excel_path，默认保存到当前目录下的translations.xlsx');
    console.log('--overwrite 参数用于覆盖已有的翻译（默认不覆盖）');
    console.log('--resource-type=TYPE 参数用于指定资源类型（默认为UNIFIED_FRONT_END）');
    process.exit(1);
  }

  const jsonFilePath = processedArgs[0];
  // 默认保存路径为当前目录下的translations.xlsx
  const excelPath = processedArgs[1] || path.join(__dirname, 'translations.xlsx');

  // 处理路径
  const currentDir = process.cwd();
  console.log(`当前工作目录: ${currentDir}`);

  // JSON文件路径处理
  let resolvedJsonPath;
  if (path.isAbsolute(jsonFilePath)) {
    resolvedJsonPath = jsonFilePath;
  } else {
    // 使用当前目录作为基准
    resolvedJsonPath = path.resolve(currentDir, jsonFilePath);
  }
  console.log(`JSON文件路径: ${resolvedJsonPath}`);

  // Excel文件路径处理
  let resolvedExcelPath;
  if (path.isAbsolute(excelPath)) {
    resolvedExcelPath = excelPath;
  } else {
    // 使用当前目录作为基准
    resolvedExcelPath = path.resolve(currentDir, excelPath);
  }
  console.log(`Excel文件路径: ${resolvedExcelPath}`);

  // 确保目录存在
  const dir = path.dirname(resolvedExcelPath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`创建目录: ${dir}`);
  }

  // 批量处理JSON文件
  await processBatchTranslations(resolvedJsonPath, resolvedExcelPath, shouldOverwrite, defaultResourceType);
}

// 执行程序
main().catch((error) => {
  console.error('程序执行错误:', error);
  process.exit(1);
});
