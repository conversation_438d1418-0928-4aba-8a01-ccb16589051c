# Excel表头多语言翻译工具

这是一个用于自动将Excel文件表头翻译为多种语言的命令行工具。该工具使用豆包AI翻译API，可以批量处理Excel文件，并生成不同语言版本的Excel文件。

## 功能特点

- 支持处理单个或多个Excel文件
- 支持自定义输出目录
- 支持选择翻译的目标语言
- 内置翻译缓存机制，减少重复翻译和API调用
- 保留原Excel文件格式和样式
- 支持指定自定义API密钥

## 安装依赖

确保已安装Node.js环境，然后安装必要的依赖：

```bash
pnpm add xlsx axios yargs
```

## 使用方法

### 基本用法

```bash
node translateExcelHeaders.js -f <Excel文件路径>
```

### 所有选项

```
选项:
  -f, --files       要翻译的Excel文件路径，可指定多个         [数组] [必需]
  -o, --outputDir   输出目录路径                   [字符串] [默认: ""]
  -l, --languages   目标语言代码列表                [数组] [默认: ["zh-CN", "en-US", "es-ES", "pt-PT", "ar-AR", "th-TH"]]
  -k, --apiKey      豆包API密钥                  [字符串] [默认: 环境变量或内置默认值]
      --help        显示帮助信息                             [布尔]
```

### 使用示例

1. 翻译单个文件：

```bash
node translateExcelHeaders.js -f path/to/file.xlsx
```

2. 翻译多个文件：

```bash
node translateExcelHeaders.js -f path/to/file1.xlsx path/to/file2.xlsx
```

3. 指定输出目录：

```bash
node translateExcelHeaders.js -f path/to/file.xlsx -o output/
```

4. 指定要翻译的目标语言：

```bash
node translateExcelHeaders.js -f path/to/file.xlsx -l en-US fr-FR de-DE
```

5. 指定自定义API密钥：

```bash
node translateExcelHeaders.js -f path/to/file.xlsx -k your-api-key
```

## 支持的语言

工具默认支持以下语言翻译：

- zh-CN (中文)
- en-US (英语)
- es-ES (西班牙语)
- pt-PT (葡萄牙语)
- ar-AR (阿拉伯语)
- th-TH (泰语)

其他可用语言：

- fr-FR (法语)
- de-DE (德语)
- it-IT (意大利语)
- ja-JP (日语)
- ko-KR (韩语)
- ru-RU (俄语)
- vi-VN (越南语)
- id-ID (印尼语)
- ms-MY (马来语)
- tr-TR (土耳其语)

## 注意事项

1. 本工具使用豆包AI接口进行翻译，需要有效的API密钥。
2. 程序会自动创建不存在的输出目录。
3. 翻译结果会保存为`原文件名_语言代码.xlsx`格式。
4. 使用缓存机制可以减少重复翻译，提高效率。

## 环境变量

可以通过环境变量设置API密钥：

```bash
export DOUBAN_API_KEY=your-api-key
```

## 示例输出

```
开始处理Excel文件...
翻译目标语言: en-US, es-ES, pt-PT, ar-AR, th-TH
使用豆包AI进行表头翻译，已启用翻译缓存
处理文件: /path/to/example.xlsx
从工作表 Sheet1 中提取了 5 个表头
【API调用统计】当前是第 1 次调用豆包API，本次翻译 5 个表头到多种语言
正在请求将 5 个表头翻译为 5 种语言...
已创建 /path/to/output/example_en_US.xlsx
已创建 /path/to/output/example_es_ES.xlsx
已创建 /path/to/output/example_pt_PT.xlsx
已创建 /path/to/output/example_ar_AR.xlsx
已创建 /path/to/output/example_th_TH.xlsx
文件 /path/to/example.xlsx 处理完成
所有文件处理完成! 总共调用豆包API 1 次 