<template>
  <div class="site-select-background-img">
    <div class="site-select-header">
      <div class="logo flex">
        <img src="../../../assets/images/logo.png" />
        <div class="ml-2 truncate md:opacity-100">
          {{ t('common.platform_title') }}
        </div>
      </div>
      <LayoutHeader fixed :isShowMenu="false" />
    </div>
    <div class="content">
      <div class="logo_text">
        <div style="font-size: 62px; font-weight: bolder; color: #000000"
          >广汽国际用户运营平台
          <div style="font-size: 26px">GAC International User Operation Platform</div>
        </div>
      </div>
      <div class="platform_content flex">
        <div class="every_platform" v-for="items in platformData" :key="items.clientId">
          <div class="content">
            <div class="title">{{ items.name }}</div>
            <div class="select" style="height: 36px">
              <a-select
                v-if="['2', '3'].includes(items.clientId)"
                style="width: 100%; height: 36px"
                :placeholder="t('common.plase_login_site_text')"
                v-model:value="items.selectSite"
              >
                <a-select-option v-for="item in items.options" :disabled="item.disabled" :key="item.url">{{ item.region }}</a-select-option>
              </a-select>
            </div>
          </div>
          <div class="goto" @click.stop="goToLink(items)">{{ t('common.unlockEntry') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="login-mini">
  import { ref, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getToken } from '/@/utils/auth';
  import LayoutHeader from '/@/layouts/default/header/index.vue';
  import { useLocaleStoreWithOut } from '/@/store/modules/locale';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { queryRegionCountryApi } from '/@/views/system/depart/depart.api';
  import { getWebconfigUrlListApi } from '/@/api/common/api';

  const { t } = useI18n();

  const permissionStore = usePermissionStore();
  const platformData = ref([] as any[]);
  platformData.value = permissionStore.getTopMenuList;
  const goToLink = (items) => {
    if (['2', '3'].includes(items.clientId) && !items.selectSite) {
      return message.warning(t('common.plase_login_site_text'));
    }
    if (items.clientId === '1') {
      window.location.href = '/';
      return;
    }
    const localeStore = useLocaleStoreWithOut();
    const [url, country] = items.selectSite?.split('____') || [];
    const newurl = /^https:\/\//.test(url) ? url : `https://${url}`;
    window.open(`${newurl}?bAccesstoken=${encodeURI(getToken())}&locale=${localeStore.getLocale}&countrycode=${country}`);
  };

  // 大区以及国家数据
  const regionCountryList = ref([] as any);
  async function getRegionCountryList() {
    try {
      const res = await queryRegionCountryApi();
      regionCountryList.value =
        res?.map((mapItems) => {
          return {
            label: mapItems.region,
            value: mapItems.regionCode,
            children:
              mapItems.countries?.map((mapItem) => {
                return {
                  label: mapItem.regionCountry,
                  value: mapItem.regionCountryCode,
                };
              }) || undefined,
          };
        }) || [];
    } catch (err) {}
  }

  onMounted(async () => {
    await getRegionCountryList();

    getWebconfigUrlListApi().then((res) => {
      if (res?.length > 0) {
        const newRes = res.map((mapItems) => {
          let region_country_value = '';
          region_country_value = region_country_value = regionCountryList.value.reduce((returnVal, nextParams) => {
            if (mapItems.region === nextParams.value) {
              returnVal = returnVal + nextParams.label + '-';
              if (nextParams.children) {
                const findParams = nextParams.children.find((findItems) => findItems.value === mapItems.country);
                if (!!findParams) {
                  returnVal = returnVal + findParams.label;
                }
              }
            }
            return returnVal;
          }, '');
          if (!region_country_value) {
            region_country_value = `${mapItems.region}-${mapItems.country}`;
          }
          return {
            ...mapItems,
            region_country: region_country_value,
          };
        });
        platformData.value.forEach((item) => {
          item.options = [];
          newRes.forEach((resItem) => {
            if (item.clientId == resItem.type) {
              item.options = [
                ...(item.options || []),
                {
                  ...resItem,
                  disabled: !resItem.usable,
                  url: `${resItem.url}____${resItem.country}`,
                },
              ];
            }
          });
        });
      }
    });
  });
</script>

<style lang="less" scoped>
  .site-select-background-img {
    .site-select-header {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 60px;
      background: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .logo {
        position: relative;
        z-index: 501;
        left: 20px;
        height: 100%;
        align-items: center;

        img {
          width: 32px;
        }
      }
    }
    > .content {
      background-image: url(../../../assets/loginmini/icon/gujiBg.png);
      background-size: cover;
      background-position: top center;
      background-repeat: no-repeat;
      min-height: 100vh;
      .logo_text {
        display: flex;
        justify-content: center;
        padding-top: 168px;
      }
    }

    .platform_content {
      width: 100%;
      padding: 100px 200px 50px;
      box-sizing: border-box;
      flex-wrap: wrap;
    }

    .every_platform {
      width: 330px;
      margin-right: 36px;
      box-sizing: border-box;
      border-radius: 20px;
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
      background-color: #fff;
      margin-bottom: 20px;
      .content {
        box-sizing: border-box;
        padding: 24px;
        .title {
          color: #4b526a;
          font-size: 18px;
          margin-bottom: 10px;
        }
      }
      .goto {
        border-top: 1px solid #f2f2f6;
        width: 100%;
        height: 48px;
        line-height: 48px;
        padding-left: 24px;
        font-size: 16px;
        color: #86909c;
        border-radius: 0 0 20px 20px;
        cursor: pointer;
        &:hover {
          background: linear-gradient(270deg, #1e91fc 0%, #5bb0ff 100%);
          color: #fff;
        }
      }
    }

    @media (max-width: 730px) {
      .platform_content {
        padding: 100px 0 50px 20px;
        justify-content: center;
      }
      .every_platform {
        width: 100%;
        padding: 40px 24px 40px;
      }
    }
  }
</style>
