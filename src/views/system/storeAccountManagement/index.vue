<template>
  <page-wrapper v-show="showCurrentRoute">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <TableAction :actions="renderTableTitleActions()" />
      </template>
      <template #action="{ record }">
        <TableAction :actions="renderTableActions(record)" />
      </template>
    </BasicTable>
  </page-wrapper>
  <router-view />
</template>

<script lang="ts" setup>
  import { onActivated, onMounted, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import dayjs from 'dayjs';
  import { useSessionStorageState } from 'vue-hooks-plus';
  import { throttle } from 'lodash-es';
  import { awaitTo } from '@ruqi/utils-admin/esm/tools';
  import { getLabelFromDict } from '@ruqi/utils-admin';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { ActionItem, BasicColumn, BasicTable, FormSchema, TableAction } from '/@/components/Table';
  import { useShowCurrentRouteByName } from '/@/hooks/route/useShowCurrentRoute';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useNationOptions, useRegionOptions } from '/@/hooks/common';
  import { useBSideUserNetworkOptions } from '/@/hooks/common/useBSideUserOptions';
  import { useStoreRoleOptions } from '/@/hooks/common/useRoleOptions';
  import { copyTextToClipboard } from '/@/hooks/web/useCopyToClipboard';
  import { AuthEnum } from '/@/enums/authEnum';
  import { PageTypeUnion } from '/@/enums/common';
  import { ValidStatusOptions, ActiveStatusOptions, ValidStatusEnum, ActiveStatusEnum } from '/@/enums/storeEnum';
  import { featchStoreUserList, featchStoreUserDelete } from '/@/api/sys/store';

  const t = useI18n('common').t;
  const router = useRouter();
  const { createConfirm, createMessage } = useMessage();
  const [, setItem] = useSessionStorageState('StoreAccountItem', {
    defaultValue: {},
  });
  const showCurrentRoute = useShowCurrentRouteByName('StoreAccount');

  const columns: BasicColumn[] = [
    {
      title: t('accountCode'),
      dataIndex: 'userCode',
      width: 100,
      resizable: true,
    },
    {
      title: t('regionText'),
      dataIndex: 'regionName',
      width: 150,
      resizable: true,
    },
    {
      title: t('countryText'),
      dataIndex: 'countryName',
      width: 150,
      resizable: true,
    },
    {
      title: t('dealerCustomerText'),
      dataIndex: 'customerName',
      width: 200,
      resizable: true,
    },
    {
      title: t('networkText'),
      dataIndex: 'networkName',
      width: 150,
      resizable: true,
    },
    {
      title: t('role_text'),
      dataIndex: 'roleName',
      width: 150,
      resizable: true,
    },
    {
      title: t('validStatus'),
      dataIndex: 'effectiveStatus',
      width: 150,
      resizable: true,
      customRender: ({ record }) => {
        return getLabelFromDict(record.effectiveStatus, ValidStatusOptions);
      },
    },
    {
      title: t('activatedStatus'),
      dataIndex: 'activationStatus',
      width: 150,
      resizable: true,
      customRender: ({ record }) => {
        return getLabelFromDict(record.effectiveStatus, ActiveStatusOptions);
      },
    },
    {
      title: t('bindAccount'),
      dataIndex: 'bindAccount',
      width: 150,
      resizable: true,
    },
    {
      title: t('createTime'),
      dataIndex: 'createTime',
      width: 150,
      resizable: true,
    },
  ];

  const { nationOptions, getData: getNationData } = useNationOptions({
    enableSearchCity: true,
  });
  const { regionOptions, getData: getRegionData } = useRegionOptions();
  const { roleOptions, getData: getRoleData } = useStoreRoleOptions();
  const { getData, bSideUserOptions, networkOptions } = useBSideUserNetworkOptions();

  const searchFormSchema: FormSchema[] = [
    {
      label: t('regionText'),
      field: 'regionCode',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: regionOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('countryText'),
      field: 'countryCode',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: nationOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('dealerCustomerText'),
      field: 'customerCode',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: bSideUserOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('networkText'),
      field: 'networkCode',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: networkOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('role_text'),
      field: 'roleId',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: roleOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('validStatus'),
      field: 'effectiveStatus',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: ValidStatusOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('activatedStatus'),
      field: 'activationStatus',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: ActiveStatusOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('createTime'),
      field: 'createTime',
      component: 'RangePicker',
      labelWidth: '100%',
      componentProps: {
        showTime: true,
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
      },
    },
  ];

  const { tableContext } = useListPage({
    designScope: 'StoreAccountManagement',
    tableProps: {
      api: async (params) => {
        if (!showCurrentRoute) return;
        return featchStoreUserList(params);
      },
      beforeFetch: (params) => {
        if (params.createTime) {
          const createTime = params.createTime.split(',');
          params.startCreateTime = dayjs(createTime[0]).valueOf();
          params.endCreateTime = dayjs(createTime[1]).valueOf();
          delete params.createTime;
        }
      },
      columns,
      formConfig: {
        schemas: searchFormSchema,
        buttonLayout: 'right',
        layout: 'vertical',
      },
      useSearchForm: true,
      showTableSetting: true,
      showActionColumn: true,
      actionColumn: {
        width: 150,
        title: t('operation'),
      },
    },
  });

  const handleRouteAction = (type: PageTypeUnion, record?: Recordable) => {
    setItem(record);
    router.push({ path: `/system/store-account/${type}` });
  };

  // 格式化输出（例如：yyyy-MM-dd HH:mm:ss）
  const formatDate = (dt) => {
    const year = dt.getFullYear();
    const month = String(dt.getMonth() + 1).padStart(2, '0');
    const day = String(dt.getDate()).padStart(2, '0');
    const hours = String(dt.getHours()).padStart(2, '0');
    const minutes = String(dt.getMinutes()).padStart(2, '0');
    const seconds = String(dt.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  const copyAccount = throttle(
    (record) => {
      // 你的逻辑代码
      const domain = window.location.origin;
      const { userCode, authPwd, authDay, createTime } = record;
      const endTime = dayjs(createTime).add(authDay, 'day').format('YYYY-MM-DD HH:mm');
      const info = `${t('loginAddress')}：
${domain}/mobile/h5Login

${t('authAccount')}：
${userCode}

${t('authPassword')}：
${authPwd}

${t('thisAuthInfoIsEffectiveFor7Days')}：${endTime}`;

      const res = copyTextToClipboard(info);
      if (res) {
        createMessage.success(t('copySuccess'));
      }
    },
    1000,
    { leading: true, trailing: false }
  );
  const renderTableActions = (record: Recordable): ActionItem[] => {
    return [
      {
        label: t('copyAccount'),
        onClick: () => {
          copyAccount(record);
        },
        auth: AuthEnum.STORE_ACCOUNT_COPY,
        ifShow: record.effectiveStatus == ValidStatusEnum.VALID && record.activationStatus == ActiveStatusEnum.WAIT,
      },
      {
        label: t('edit'),
        onClick: () => {
          handleRouteAction('edit', record);
        },
        auth: AuthEnum.STORE_ACCOUNT_EDIT,
        ifShow: record.effectiveStatus == ValidStatusEnum.VALID && record.activationStatus == ActiveStatusEnum.WAIT,
      },
      {
        label: t('delText'),
        color: 'error',
        onClick: async () => {
          createConfirm({
            title: t('okTextDelete'),
            iconType: 'warning',
            content: `${t('deleteTip')}`,
            okText: t('okText'),
            cancelText: t('cancelText'),
            onOk: async () => {
              const [err] = await awaitTo(featchStoreUserDelete(record.id));
              if (!err) {
                createMessage.success(t('operationSuccess'));
                reload();
              }
            },
          });
        },
        auth: AuthEnum.STORE_ACCOUNT_DELETE,
        ifShow: record.effectiveStatus == ValidStatusEnum.VALID && record.activationStatus == ActiveStatusEnum.WAIT,
      },
    ];
  };
  const [registerTable, { reload }] = tableContext;

  const renderTableTitleActions = (): ActionItem[] => {
    return [
      {
        label: t('add'),
        size: 'middle',
        onClick: () => {
          handleRouteAction('add');
        },
        auth: AuthEnum.STORE_ACCOUNT_ADD,
        type: 'primary',
      },
    ];
  };

  // 更新 options
  onMounted(async () => {
    await awaitTo(Promise.all([getNationData(), getRegionData(), getData(), getRoleData()]));
  });
  onActivated(async () => {
    reload && reload();
  });
</script>

<style lang="scss" scoped></style>
