<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="t('common.static_state_route_forget_password')"
    @ok="handleSubmit"
    width="600px"
    :maxHeight="466"
  >
    <BasicForm @register="registerForm" style="padding-left: 36px">
      <template #custom="{ model }">
        <div class="flex">
          <a-select
            style="width: 168px; margin-right: 10px"
            v-if="model.fangshi === 'phone'"
            v-model:value="quhaoVal"
            :showSearch="true"
            :placeholder="t('common.global_roaming')"
            :options="quhaoOptions"
          />
          <a-input type="text" :placeholder="t('common.accountPlaceholder')" v-model:value="accountValue" />
        </div>
      </template>
      <template #password="{ model, field }">
        <a-input type="password" v-model:value="model[field]" />
        <div style="margin-top: 20px; color: #86909c; font-size: 16px; line-height: 30px">
          <div>{{ t('common.password_rule1') }}</div>
          <div>{{ t('common.password_rule2') }}</div>
        </div>
      </template>
      <template #newPassword="{ model, field }">
        <a-input type="password" v-model:value="model[field]" />
      </template>
      <template #code="{ model, field }">
        <div class="flex">
          <a-input
            style="width: 186px"
            class="fix-auto-fill"
            type="text"
            :placeholder="t('common.inputCodePlaceholder')"
            v-model:value="model[field]"
          />
          <div class="aui-code" style="margin-top: 5px">
            <span
              v-if="isShowGetCode === true"
              style="margin-left: 5px; cursor: pointer; font-size: 14px; color: #1990ff"
              @click.stop="handleChangeCheckCode"
              >{{ t('common.obtain_verification_code_text') }}</span
            >
            <StatisticCountdown
              v-if="isShowGetCode === false"
              :valueStyle="{ color: '#1990FF', fontSize: '14px', marginLeft: '5px' }"
              :value="time"
              :format="`ss${t('common.retrieveSsSecondsLater')}`"
              @finish="isShowGetCode = true"
            />
          </div>
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { StatisticCountdown } from 'ant-design-vue';
  import { passwordRule } from '/@/utils/validator';
  import { rules } from '/@/utils/helper/validator';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { useForm } from '/@/components/Form/src/hooks/useForm';
  import { forgetSendSmsApi, queryAreaListApi } from '/@/api/common/api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import rsaEncryptor from '/@/utils/rsaEncrypt';

  const { t } = useI18n();

  const { createMessage } = useMessage();

  const $message = useMessage();

  // 账号输入
  const quhaoVal = ref(null as any);
  const quhaoOptions = ref([]);
  const accountValue = ref('');

  // 是否展示重新获取验证码
  const isShowGetCode = ref(true);
  const loading = ref(false);

  const time = ref(Date.now() + 1000 * 60);

  /**
   * 获取验证码
   */
  async function handleChangeCheckCode() {
    const values = getFieldsValue();
    let newAccountValue = '';
    if (!accountValue.value) {
      return $message.createMessage.warning(t('common.accountPlaceholder'));
    }
    if (values.fangshi === 'phone') {
      if (!quhaoVal.value) {
        return $message.createMessage.warning(`${t('common.chooseText')}${t('common.global_roaming')}`);
      }
      newAccountValue = quhaoVal.value + accountValue.value;
    } else {
      newAccountValue = accountValue.value;
    }
    if (loading.value) {
      return;
    }
    loading.value = true;
    try {
      await forgetSendSmsApi({
        account: newAccountValue,
      });
      createMessage.success(t('common.verification_code_sent_successfully'));
      isShowGetCode.value = false;
      loading.value = false;
      time.value = Date.now() + 1000 * 60;
    } catch (err) {}
  }

  // 获取国际区号
  async function queryAreaListFn() {
    try {
      const res = await queryAreaListApi();
      quhaoOptions.value =
        res?.map((mapItems) => {
          return {
            label: mapItems.areaCode,
            value: mapItems.areaCode,
          };
        }) || [];
    } catch (err) {}
  }

  //表单配置
  const [registerForm, { resetFields, validate, getFieldsValue, clearValidate }] = useForm({
    schemas: [
      {
        label: t('common.login_options'),
        field: 'fangshi',
        component: 'Select',
        defaultValue: 'phone',
        required: true,
        labelWidth: '100%',
        componentProps: {
          allowClear: false,
          placeholder: t('common.chooseText'),
          options: [
            {
              label: t('common.phone'),
              value: 'phone',
            },
            {
              label: t('common.email_text'),
              value: 'email',
            },
          ],
        },
      },
      {
        label: t('common.account_text'),
        field: 'custom',
        component: 'Input',
        labelWidth: '100%',
        required: true,
        defaultValue: '1',
        slot: 'custom',
      },
      {
        label: t('common.changePassword_newPassword'),
        field: 'password',
        slot: 'password',
        component: 'Input',
        componentProps: {
          placeholder: t('common.changePassword_pleaseEnterNewPassword'),
        },
        labelWidth: '100%',
        rules: [
          {
            required: true,
            message: t('common.changePassword_pleaseEnterNewPassword'),
          },
        ],
      },
      {
        label: t('common.changePassword_confirmNewPassword'),
        field: 'confirmpassword',
        component: 'Input',
        labelWidth: '100%',
        slot: 'newPassword',
        dynamicRules: ({ values }) => rules.confirmPassword(values, true),
      },
      {
        label: t('common.inputCode'),
        field: 'code',
        labelWidth: '100%',
        component: 'Input',
        required: true,
        slot: 'code',
      },
    ],
    showActionButtonGroup: false,
    buttonLayout: 'right',
    layout: 'vertical',
  });
  // 表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (_data) => {
    isShowGetCode.value = true;
    loading.value = false;
    time.value = Date.now() + 1000 * 60;
  });

  // 表单提交事件
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      if (!passwordRule.test(values.confirmpassword)) {
        return $message.createMessage.warning(`${t('common.password_rule1')};${t('common.password_rule2')}`);
      }
      if (!accountValue.value) {
        return $message.createMessage.warning(t('common.accountPlaceholder'));
      }
      let newAccountValue = '';
      if (values.fangshi === 'phone') {
        if (!quhaoVal.value) {
          return $message.createMessage.warning(`${t('common.chooseText')}${t('common.global_roaming')}`);
        }
        newAccountValue = quhaoVal.value + accountValue.value;
      } else {
        newAccountValue = accountValue.value;
      }
      //提交表单
      let params = {
        code: values.code,
        password: rsaEncryptor.encrypt(values.confirmpassword),
        account: newAccountValue,
      };
      defHttp.post({ url: '/forgotPwd', params }, { isTransformResponse: false }).then((res) => {
        if (res.code === 0) {
          $message.createMessage.success(res.msg);
          //关闭弹窗
          closeModal();
        } else {
          $message.createMessage.warning(res.msg);
        }
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  async function show() {
    setModalProps({ visible: true });
    accountValue.value = '';
    quhaoVal.value = null;
    quhaoOptions.value = [];
    isShowGetCode.value = true;
    loading.value = false;
    time.value = Date.now() + 1000 * 60;
    await queryAreaListFn();
    await resetFields();
    await clearValidate();
  }

  defineExpose({
    show,
  });
</script>
