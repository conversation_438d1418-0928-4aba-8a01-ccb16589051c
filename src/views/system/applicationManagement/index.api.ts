import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();

/**
 * 系统应用列表
 * @param params
 */
export const webconfigListApi = (params: { pageIndex: number; pageSize: number; type?: number; country?: string; url?: string; status?: number }) =>
  defHttp.post({ url: '/webconfig/page', params });

/**
 * 系统应用列表-更新
 * @param params
 */
export const updateWebconfigApi = (params) => defHttp.post({ url: '/webconfig/update', params });

/**
 * 系统应用列表-保存
 * @param params
 */
export const saveWebconfigApi = (params) => defHttp.post({ url: '/webconfig/save', params });

/**
 * 系统应用列表-修改状态
 * @param params
 */
export const updateStatusApi = (
  params: {
    id: string;
    status: 1 | 2; // 1启用 2禁用
  },
  handleSuccess
) => {
  Modal.confirm({
    title: params.status === 1 ? t('common.enableText') : t('common.disableText'),
    content: `${t('common.okText')}${params.status === 1 ? t('common.enableText') : t('common.disableText')}?`,
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      return defHttp.post({ url: '/webconfig/updateStatus', data: params }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 删除应用
 * @param params
 */
export const deleteApi = (params: { id: string }, handleSuccess) => {
  Modal.confirm({
    title: t('common.okTextDelete'),
    content: `${t('common.deleteTip')}`,
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      return defHttp.post({ url: '/webconfig/delete', data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
