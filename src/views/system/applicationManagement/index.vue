<template>
  <BasicTable @register="registerTable">
    <template #tableTitle>
      <a-button auth="'system:applicationManagement:add'" type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">
        {{ t('common.add') }}</a-button
      >
    </template>
    <template #action="{ record }">
      <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
    </template>
  </BasicTable>
  <!-- 应用新增、编辑 -->
  <HandleModal @register="registerHandleModal" @success="reload" />
</template>
<script lang="ts" name="applicationManagement" setup>
  import { ref } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import HandleModal from './components/HandleModal.vue';
  import { columns, searchFormSchema } from './index.data';
  import { updateStatusApi, webconfigListApi, deleteApi } from './index.api';
  import { queryRegionCountryApi } from '/@/views/system/depart/depart.api';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();

  // 大区以及国家数据
  const regionCountryList = ref([] as any);
  async function getRegionCountryList() {
    try {
      const res = await queryRegionCountryApi();
      regionCountryList.value =
        res?.map((mapItems) => {
          return {
            label: mapItems.region,
            value: mapItems.regionCode,
            children:
              mapItems.countries?.map((mapItem) => {
                return {
                  label: mapItem.regionCountry,
                  value: mapItem.regionCountryCode,
                };
              }) || undefined,
          };
        }) || [];
    } catch (err) {}
  }

  // 新增、编辑应用弹窗
  const [registerHandleModal, { openModal: openHandleModal }] = useModal();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'applicationManagement-template',
    tableProps: {
      api: webconfigListApi,
      columns: columns,
      formConfig: {
        buttonLayout: 'right',
        schemas: searchFormSchema,
        layout: 'vertical',
        autoAdvancedCol: 4,
      },
      actionColumn: {
        width: 96,
      },
      beforeFetch: (params) => {
        let newParams = {} as any;
        if (params.country) {
          newParams.country = params.country.split(',').map((mapItems) => {
            return mapItems.split('____')[1];
          });
        }
        return {
          ...params,
          ...newParams,
        };
      },
      afterFetch: async (res) => {
        await getRegionCountryList();
        return res.map((mapItems) => {
          let region_country_value = '';
          region_country_value = region_country_value = regionCountryList.value.reduce((returnVal, nextParams) => {
            if (mapItems.region === nextParams.value) {
              returnVal = returnVal + nextParams.label + '-';
              if (nextParams.children) {
                const findParams = nextParams.children.find((findItems) => findItems.value === mapItems.country);
                if (!!findParams) {
                  returnVal = returnVal + findParams.label;
                }
              }
            }
            return returnVal;
          }, '');
          if (!region_country_value) {
            region_country_value = `${mapItems.region}-${mapItems.country}`;
          }
          return {
            ...mapItems,
            region_country: region_country_value,
          };
        });
      },
    },
  });
  const [registerTable, { reload }] = tableContext;

  /**
   * 新增
   */
  function handleCreate() {
    openHandleModal(true, {
      handleType: 'add',
    });
  }
  /**
   * 编辑
   */
  function handleEdit(record: Recordable) {
    openHandleModal(true, {
      ...record,
      handleType: 'edit',
    });
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      // 编辑
      {
        label: t('common.edit'),
        onClick: handleEdit.bind(null, record),
        auth: 'system:applicationManagement:edit',
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      // 删除
      {
        label: t('common.delText'),
        onClick: handleDelete.bind(null, record),
        auth: 'system:applicationManagement:del',
        ifShow: () => [2, null].includes(record.status),
      },
      // 启用
      {
        label: t('common.enableText'),
        onClick: handleStatusChange.bind(null, record),
        ifShow: () => [2, null].includes(record.status),
        auth: 'system:applicationManagement:status',
      },
      // 禁用
      {
        label: t('common.disableText'),
        onClick: handleStatusChange.bind(null, record),
        ifShow: () => record.status === 1,
        auth: 'system:applicationManagement:status',
      },
    ];
  }

  /**
   * 删除
   */
  async function handleDelete(record) {
    deleteApi({ id: record.id }, reload);
  }

  // 禁用启用
  async function handleStatusChange(record) {
    const status = record.status === 1 ? 2 : 1;
    updateStatusApi({ id: record.id, status }, reload);
  }
</script>
