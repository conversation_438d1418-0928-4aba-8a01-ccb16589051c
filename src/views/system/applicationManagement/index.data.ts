import { FormSchema } from '/@/components/Table';
import { queryRegionCountryApi } from '/@/views/system/depart/depart.api';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

// 应用列表
export const columns = [
  {
    title: t('common.application_code_text'),
    dataIndex: 'type',
    width: 100,
    customRender: ({ text }) => {
      let returnText = '';
      switch (text) {
        case 2:
          returnText = t('common.app_managementBackend');
          break;
        case 3:
          returnText = t('common.officeWeb_managementBackend');
      }
      return returnText;
    },
  },
  {
    title: t('common.region_country_text'),
    dataIndex: 'region_country',
    width: 100,
  },
  {
    title: t('common.jump_address'),
    dataIndex: 'url',
    width: 100,
  },
  {
    title: t('common.status'),
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => {
      return text === 1 ? t('common.enableText') : t('common.disableText');
    },
  },
];

// 应用列表搜索
export const searchFormSchema: FormSchema[] = [
  {
    field: 'type',
    label: t('common.application_code_text'),
    component: 'Select',
    colProps: { span: 6 },
    labelWidth: '100%',
    componentProps: {
      options: [
        {
          label: t('common.app_managementBackend'),
          value: 2,
        },
        {
          label: t('common.officeWeb_managementBackend'),
          value: 3,
        },
      ],
    },
  },
  {
    label: t('common.region_country_text'),
    field: 'country',
    component: 'ApiTreeSelect',
    labelWidth: '100%',
    componentProps: {
      showSearch: true,
      multiple: true,
      api: queryRegionCountryApi,
      customReturnFn: (res) => {
        return (
          res?.map((mapItems) => {
            return {
              label: mapItems.region,
              value: mapItems.regionCode,
              disabled: true,
              children:
                mapItems.countries?.map((mapItem) => {
                  return {
                    label: mapItem.regionCountry,
                    value: `${mapItems.regionCode}____${mapItem.regionCountryCode}`,
                  };
                }) || undefined,
            };
          }) || []
        );
      },
      dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'url',
    label: t('common.jump_address'),
    labelWidth: '100%',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'status',
    label: t('common.status'),
    labelWidth: '100%',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: t('common.enableText'),
          value: 1,
        },
        {
          label: t('common.disableText'),
          value: 2,
        },
      ],
    },
    colProps: { span: 6 },
  },
];

// 新增、编辑应用
export const formSchema: FormSchema[] = [
  {
    field: 'type',
    label: t('common.application_code_text'),
    required: true,
    component: 'Select',
    labelWidth: '100%',
    componentProps: {
      options: [
        {
          label: t('common.app_managementBackend'),
          value: 2,
        },
        {
          label: t('common.officeWeb_managementBackend'),
          value: 3,
        },
      ],
    },
  },
  {
    label: t('common.region_country_text'),
    field: 'region_country',
    component: 'ApiTreeSelect',
    labelWidth: '100%',
    required: true,
    componentProps: {
      showSearch: true,
      multiple: true,
      api: queryRegionCountryApi,
      customReturnFn: (res) => {
        return (
          res?.map((mapItems) => {
            return {
              label: mapItems.region,
              value: mapItems.regionCode,
              disabled: true,
              children:
                mapItems.countries?.map((mapItem) => {
                  return {
                    label: mapItem.regionCountry,
                    value: `${mapItems.regionCode}____${mapItem.regionCountryCode}`,
                  };
                }) || undefined,
            };
          }) || []
        );
      },
      dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
    },
  },
  {
    field: 'url',
    label: t('common.jump_address'),
    labelWidth: '100%',
    required: true,
    component: 'Input',
  },
];
