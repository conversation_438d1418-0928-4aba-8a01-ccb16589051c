<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :width="660" :title="title" @ok="handleSubmit" destroyOnClose>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { message } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../index.data';
  import { saveWebconfigApi, updateWebconfigApi } from '../index.api';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();

  // 声明Emits
  const emit = defineEmits(['success', 'register']);

  const title = computed(() => {
    let titleVal = '';
    switch (handleType.value) {
      case 'add':
        titleVal = t('common.add');
        break;
      case 'edit':
        titleVal = t('common.edit');
        break;
    }
    return titleVal;
  });

  // 编辑、查看
  let editId = '';
  // 操作类型
  let handleType = ref('');

  // 表单配置
  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
    layout: 'vertical',
  });

  // 表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: true, loading: true });
    editId = data?.id || '';
    handleType.value = data?.handleType || 'add';
    //表单赋值git
    if (data?.handleType !== 'add') {
      await setFieldsValue({
        url: data?.url || undefined,
        type: data?.type || undefined,
        region_country: data?.region && data?.country ? `${data.region}____${data.country}` : undefined,
      });
    }

    const newFormSchema = formSchema.map((mapItems) => {
      let dynamicDisabled = data?.handleType === 'view';
      if (['region_country', 'type'].includes(mapItems.field)) {
        if (data?.handleType !== 'add') {
          dynamicDisabled = true;
        }
      }
      return {
        ...mapItems,
        dynamicDisabled,
      };
    });
    await updateSchema(newFormSchema);
    setModalProps({ confirmLoading: false, loading: false });
  });
  // 表单提交事件
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true, loading: true });
      // 提交表单
      const regionCountrys = values.region_country?.split(',') || [];
      const submitDatas = regionCountrys.map((mapItems) => {
        const [region, country] = mapItems?.split('____') || [];
        return {
          ...values,
          id: editId,
          region,
          country,
        };
      });
      handleType.value === 'add' ? await saveWebconfigApi(submitDatas) : await updateWebconfigApi(submitDatas[0]);
      message.success(t('common.operation_successfull'));
      // 关闭弹窗
      closeModal();
      // 刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false, loading: false });
    }
  }
</script>
