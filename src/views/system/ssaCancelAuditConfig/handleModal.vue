<template>
  <div>
    <BasicModal
      v-bind="attrs"
      @register="register"
      :width="600"
      :height="400"
      :maximize="false"
      destroyOnClose
      :mask-closable="false"
      @cancel="handleClose"
      @ok="handleOk"
      :canFullscreen="false"
      :title="t('countryConfig')"
    >
      <BasicForm @register="registerForm">
        <template #title>
          <h3 style="margin-top: 10px">{{ t('globalConfig') }}</h3>
        </template>
      </BasicForm>
    </BasicModal>
  </div>
</template>
<script setup lang="ts">
  import { reactive, computed, ref } from 'vue';
  import { awaitTo } from '@ruqi/utils-admin';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useForm, BasicForm, FormSchema } from '/@/components/Form';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { featchCancelAuditAdd, featchCancelAuditEdit } from '@/api/sys/store';
  import { rules } from '/@/utils/helper/validator';

  const { createMessage } = useMessage();
  const { success, error } = createMessage;
  const { t } = useI18n('common');

  let pageData: any = reactive({});
  const countryType = ref(1);

  const props = defineProps({
    nationOptions: {
      type: Array,
      default: () => [],
    },
  });
  const [register, { closeModal }] = useModalInner((data) => {
    if (data) {
      pageData = { ...data, countryCode: data?.countryCode?.split(',') };
      countryType.value = data.type;
      data.id && setFieldsValue(pageData);
    }
  });
  const emits = defineEmits(['success']);
  const formSchemas = computed<FormSchema[]>(() => [
    {
      label: '',
      field: 'titleSolt',
      slot: 'title',
      component: 'Input',
      ifShow: !countryType.value,
    },
    {
      label: t('countryText'),
      field: 'countryCode',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: props.nationOptions,
        showSearch: true,
        mode: 'multiple',
      },
      required: true,
      ifShow: !!countryType.value,
    },
    {
      label: t('auditConfig'),
      field: 'standardValue',
      labelWidth: '100%',
      component: 'Input',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        suffix: t('time_hours'),
      },
      required: true,
      dynamicRules: () => {
        return [{ ...rules.number(false)[0], trigger: 'blur', required: false }];
      },
    },
  ]);

  const [registerForm, { setFieldsValue, validate }] = useForm({
    schemas: formSchemas,
    showResetButton: false,
    showCancelButton: false,
    showSubmitButton: false,
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
  });
  const attrs = useAttrs();

  /** 关闭弹窗 */
  const handleClose = () => {
    closeModal();
  };

  const handleOk = () => {
    validate().then(async (data) => {
      console.log(data);
      const api = pageData?.id ? featchCancelAuditEdit : featchCancelAuditAdd;
      const countryCode = data?.countryCode || pageData?.countryCode?.join(',') || '';
      const params = {
        ...data,
        type: pageData?.type === 0 ? 0 : 1,
        countryCode: countryCode,
      };
      if (pageData?.id) {
        params.id = pageData?.id;
      }
      const [err] = await awaitTo(api({ ...params }));
      if (!err) {
        success(t('common.operationSuccess'));
        emits('success');
        closeModal();
      }
    });
  };
</script>

<style scoped></style>
