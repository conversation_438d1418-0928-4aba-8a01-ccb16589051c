<template>
  <BasicTable @register="registerTable">
    <!--插槽:table标题-->
    <template #tableTitle>
      <a-button type="primary" preIcon="ant-design:export-outlined" @click="toExportXls" v-auth="'sys:user:import'">
        {{ t('common.export') }}</a-button
      >
    </template>
  </BasicTable>
</template>
<script lang="ts" name="auditoraLog" setup>
  import { onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { BasicTable } from '/@/components/Table';
  import { LOCALE_KEY } from '/@/enums/cacheEnum';
  import { localeSetting } from '/@/settings/localeSetting';
  import { createLocalStorage } from '/@/utils/cache';
  import { columns, searchFormSchema } from './index.data';
  import { queryLogInfoApi, querySjLogExport } from '/@/api/common/api';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { getAppEnvConfig } from '/@/utils/env';
  import dayjs from 'dayjs';
  import { store } from '/@/store';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getBackMenuAndPerms } from '/@/api/sys/menu';
  import { addSjLogFn } from '/@/api/common/api';

  const { t } = useI18n();

  // 获取当前用户所拥有的菜单
  async function getMenuFn() {
    let returnData = [];
    try {
      const systemPermission: any = await getBackMenuAndPerms({
        userId: store?.state.value?.['app-user']?.userInfo?.userId,
      });
      if (systemPermission.menu) {
        returnData = systemPermission.menu?.children?.find((findItems) => findItems.id === '111')?.children || [];
      }
    } catch (err) {}

    setProps({
      formConfig: {
        schemas: searchFormSchema.map((mapItems) => {
          if (mapItems.field == 'remarker') {
            // @ts-ignore
            mapItems.componentProps.onSearch = (val: string) => {
              setProps({
                formConfig: {
                  schemas: searchFormSchema.map((mapItems) => {
                    if (mapItems.field == 'remarker') {
                      // @ts-ignore
                      mapItems.componentProps.treeData = filterTreeData(originTreeData.value, val);
                    }
                    return mapItems;
                  }),
                  buttonLayout: 'right',
                  layout: 'vertical',
                  showAdvancedButton: true,
                },
              });
            };
            // @ts-ignore
            mapItems.componentProps.treeData = returnData;
          }
          return mapItems;
        }),
        buttonLayout: 'right',
        layout: 'vertical',
        showAdvancedButton: true,
      },
    });
  }
  onMounted(() => {
    getMenuFn();
  });

  // 审计日志当前操作页面-菜单
  const remarkLogStr = JSON.stringify(useRoute()?.meta?.originalTitles || []);

  // 存储的当前语言
  const ls = createLocalStorage();
  const languageType = (ls.get(LOCALE_KEY) || localeSetting)?.locale || 'en-US';

  /**
   * 获取语言包
   */
  const getMultiLingualApi = (params: { resourceType: string }) => {
    return new Promise((resolve, reject) => {
      const { VITE_GLOB_DOMAIN_URL } = getAppEnvConfig();
      fetch(`${VITE_GLOB_DOMAIN_URL}/manage/iop/language/v1/multiLingual/queryListByResourceType/${params.resourceType}`, {
        method: 'POST',
      })
        .then((response) => {
          return response.json();
        })
        .then((data) => {
          if (data?.code === 0) {
            let params = {
              common: {},
            };
            data.content.forEach((items: any) => {
              params.common[items.code] = {};
              items?.valueLanguageResponses?.forEach((item: any) => {
                params.common[items.code][item.language] = item.value || undefined;
              });
            });
            resolve(data);
            return;
          }
          reject(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 导出
  const toExportXls = () => {
    onExportXls({
      beforeExportXls: handleSubmitParams,
    });
    addSjLogFn(remarkLogStr, '导出');
    reload();
  };

  const handleSubmitParams = (params: any) => {
    if (!!params.createTime) {
      const [createTimeStart, createTimeEnd] = params.createTime.split(',');
      params.createTimeStart = dayjs(createTimeStart).valueOf();
      params.createTimeEnd = dayjs(createTimeEnd).valueOf();
    }
    params.bizType = 0;
  };

  // 列表页面公共参数、方法
  const { tableContext, onExportXls } = useListPage({
    designScope: 'auditoraLog-template',
    exportConfig: {
      methods: 'post',
      url: querySjLogExport,
    },
    tableProps: {
      api: queryLogInfoApi,
      columns: columns,
      formConfig: {
        schemas: searchFormSchema,
        buttonLayout: 'right',
        layout: 'vertical',
        showAdvancedButton: true,
      },
      showActionColumn: false,
      beforeFetch: handleSubmitParams,
      afterFetch: async (data) => {
        let resMenuParams = {} as any;
        try {
          const menuData: any = await getMultiLingualApi({
            resourceType: 'MENU',
          });
          let params = {
            menu: {},
          };
          menuData.content.forEach((items: any) => {
            params.menu[items.code] = {};
            items?.valueLanguageResponses?.forEach((item: any) => {
              params.menu[items.code][item.language] = item.value || undefined;
            });
          });
          resMenuParams = params;
        } catch (err) {}
        return data.map((mapItems) => {
          let logTypeLanguageKey = '';
          switch (mapItems.logType) {
            case '新增':
              logTypeLanguageKey = 'add';
              break;
            case '编辑':
              logTypeLanguageKey = 'edit';
              break;
            case '查看':
              logTypeLanguageKey = 'view';
              break;
            case '删除':
              logTypeLanguageKey = 'delText';
              break;
            case '导入':
              logTypeLanguageKey = 'import';
              break;
            case '导出':
              logTypeLanguageKey = 'export';
              break;
            case '设置别名':
              logTypeLanguageKey = 'setAlias';
              break;
            default:
              '';
          }
          const meunList = !!mapItems.remarker ? JSON.parse(mapItems.remarker) : [];
          const remarker = meunList
            .map((mapItems) => {
              const findLanauageMenuParams = resMenuParams.menu?.[mapItems] || {};
              let thisValue = '';
              if (findLanauageMenuParams) {
                thisValue = languageType === 'zh-CN' ? mapItems : findLanauageMenuParams[languageType];
              } else {
                thisValue = mapItems;
              }
              return thisValue;
            })
            ?.join('-');
          return {
            ...mapItems,
            logType: t(`common.${logTypeLanguageKey}`),
            remarker,
          };
        });
      },
    },
  });
  const [registerTable, { reload, setProps }] = tableContext;
</script>
