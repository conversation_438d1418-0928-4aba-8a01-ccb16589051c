import { FormSchema } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

// 审计日志列表
export const columns = [
  {
    title: t('common.meun_text'),
    dataIndex: 'remarker',
    width: 100,
  },
  {
    title: t('common.operationType'),
    dataIndex: 'logType',
    width: 100,
  },
  {
    title: t('common.person_code_text'),
    dataIndex: 'bizId',
    width: 100,
  },
  {
    title: t('common.user_name_text'),
    dataIndex: 'cnName',
    width: 120,
  },
  {
    title: t('common.operationTime'),
    dataIndex: 'createTime',
    width: 120,
  },
];

// 审计日志列表搜索
export const searchFormSchema: FormSchema[] = [
  {
    field: 'remarker',
    label: t('common.meun_text'),
    component: 'TreeSelect',
    colProps: { span: 8 },
    labelWidth: '100%',
    componentProps: {
      placeholder: t('common.chooseText'),
      showSearch: true,
      treeData: [],
      fieldNames: { label: 'text', value: 'cname', options: 'children' },
      dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'logType',
    label: t('common.operationType'),
    component: 'Select',
    colProps: { span: 8 },
    labelWidth: '100%',
    componentProps: {
      options: [
        {
          label: t('common.add'),
          value: '新增',
        },
        {
          label: t('common.edit'),
          value: '编辑',
        },
        {
          label: t('common.view'),
          value: '查看',
        },
        {
          label: t('common.delText'),
          value: '删除',
        },
        {
          label: t('common.import'),
          value: '导入',
        },
        {
          label: t('common.export'),
          value: '导出',
        },
      ],
    },
  },
  {
    label: t('common.operationTime'),
    field: 'createTime',
    component: 'RangeDate',
    labelWidth: '100%',
    componentProps: {
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
    },
    colProps: { span: 8 },
  },
  {
    field: 'cnName',
    label: t('common.operator'),
    component: 'Input',
    colProps: { span: 8 },
    labelWidth: '100%',
  },
  {
    field: 'bizId',
    label: t('common.person_code_text'),
    component: 'Input',
    colProps: { span: 8 },
    labelWidth: '100%',
  },
];
