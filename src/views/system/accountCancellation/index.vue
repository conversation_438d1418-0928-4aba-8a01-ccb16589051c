<template>
  <page-wrapper v-show="showCurrentRoute">
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction :actions="renderTableActions(record)" />
      </template>
    </BasicTable>

    <AuditModal @register="registerModal" @success="reload" />
  </page-wrapper>
  <router-view />
</template>

<script lang="ts" setup>
  import { onMounted } from 'vue';
  import dayjs from 'dayjs';
  import { awaitTo } from '@ruqi/utils-admin/esm/tools';
  import { getLabelFromDict } from '@ruqi/utils-admin';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { useModal } from '/@/components/Modal';
  import { ActionItem, BasicColumn, BasicTable, FormSchema, TableAction } from '/@/components/Table';
  import { useShowCurrentRouteByName } from '/@/hooks/route/useShowCurrentRoute';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useNationOptions, useRegionOptions } from '/@/hooks/common';
  import { useBSideUserNetworkOptions } from '/@/hooks/common/useBSideUserOptions';
  import { AuthEnum } from '/@/enums/authEnum';
  import { AuditResultOptions, AuditResultEnum } from '/@/enums/storeEnum';
  import { featchCancelUserList } from '/@/api/sys/store';
  import AuditModal from './auditModal.vue';

  const [registerModal, { openModal: openModal }] = useModal();

  const t = useI18n('common').t;
  const showCurrentRoute = useShowCurrentRouteByName('AccountCancellation');

  const columns: BasicColumn[] = [
    {
      title: t('accountCode'),
      dataIndex: 'userCode',
      width: 100,
      resizable: true,
    },
    {
      title: t('email'),
      dataIndex: 'email',
      width: 150,
      resizable: true,
    },
    {
      title: t('networkText'),
      dataIndex: 'networkName',
      width: 150,
      resizable: true,
    },
    {
      title: t('role_text'),
      dataIndex: 'roleName',
      width: 150,
      resizable: true,
    },
    {
      title: t('cancelTime'),
      dataIndex: 'createTime',
      width: 150,
      resizable: true,
    },
    {
      title: t('auditStatus'),
      dataIndex: 'status',
      width: 150,
      resizable: true,
      customRender: ({ record }) => {
        return getLabelFromDict(record.status, AuditResultOptions);
      },
    },
  ];

  const { nationOptions, getData: getNationData } = useNationOptions({
    enableSearchCity: true,
  });
  const { regionOptions, getData: getRegionData } = useRegionOptions();
  const { getData, bSideUserOptions, networkOptions } = useBSideUserNetworkOptions();

  const searchFormSchema: FormSchema[] = [
    {
      label: t('regionText'),
      field: 'regionCode',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: regionOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('countryText'),
      field: 'countryCode',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: nationOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('dealerCustomerText'),
      field: 'customer',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: bSideUserOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
    {
      label: t('networkText'),
      field: 'network',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: networkOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },

    {
      label: t('auditStatus'),
      field: 'status',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: AuditResultOptions,
        showSearch: true,
        mode: 'multiple',
      },
    },
  ];

  const { tableContext } = useListPage({
    designScope: 'AccountCancellation',
    tableProps: {
      api: async (params) => {
        if (!showCurrentRoute) return;
        return featchCancelUserList(params);
      },
      columns,
      formConfig: {
        schemas: searchFormSchema,
        buttonLayout: 'right',
        layout: 'vertical',
      },
      useSearchForm: true,
      showTableSetting: true,
      showActionColumn: true,
      actionColumn: {
        width: 150,
        title: t('operation'),
      },
    },
  });

  const renderTableActions = (record: Recordable): ActionItem[] => {
    return [
      {
        label: t('view'),
        onClick: () => {
          openModal(true, { ...record, type: 'view' });
        },
        auth: AuthEnum.ACCOUNT_CANCELLATION_VIEW,
      },
      {
        label: t('auditText'),
        onClick: () => {
          openModal(true, { ...record, type: 'audit' });
        },
        auth: AuthEnum.ACCOUNT_CANCELLATION_AUDIT,
        ifShow: record.status == AuditResultEnum.WAIT,
      },
    ];
  };
  const [registerTable, { reload }] = tableContext;

  // 更新 options
  onMounted(async () => {
    await awaitTo(Promise.all([getNationData(), getRegionData(), getData()]));
  });
</script>

<style lang="scss" scoped></style>
