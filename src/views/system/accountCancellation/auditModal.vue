<template>
  <div>
    <BasicModal
      v-bind="attrs"
      @register="register"
      :width="600"
      :maximize="false"
      destroyOnClose
      :mask-closable="false"
      @cancel="handleClose"
      @ok="handleOk"
      :canFullscreen="false"
      :title="pageType === 'audit' ? t('audit') : t('view')"
      :showCancelBtn="pageType === 'audit'"
      :showOkBtn="pageType === 'audit'"
    >
      <div class="audit_modal">
        <p v-for="(item, index) in auditInfoAll" :key="index">
          <span class="label">{{ t(item.label) }}：</span>
          <span class="value">{{ item.format ? item.format(detail[item.value]) : detail[item.value] }}</span>
          <span class="red_text" v-if="item.isCancel && detail?.status == AuditResultEnum.WAIT"
            >{{ formatMinutesToHourMinute(detail.approvalApprovalTime, t) }}{{ t('approvalAutoTimePass') }}</span
          >
        </p>

        <BasicForm v-if="pageType === 'audit'" @register="registerForm" />
      </div>
    </BasicModal>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'BatchImportModal',
    components: { BasicModal },
  };
</script>
<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { awaitTo, getLabelFromDict } from '@ruqi/utils-admin';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useForm, BasicForm, FormSchema } from '/@/components/Form';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { featchtUncompletedClue, featchtUncompletedOrder, featchCancelUserAudit } from '/@/api/sys/store';
  import { AuditStatusOptions, AuditResultEnum, AuditWayOptions } from '/@/enums/storeEnum';
  import { formatMinutesToHourMinute } from '/@/utils';
  const { createMessage } = useMessage();
  const { success } = createMessage;
  const t = useI18n('common').t;

  // 页面类型
  const pageType = ref('');
  const auditInfoAll: any = ref([]);
  const auditInfo = [
    {
      label: 'bindAccount',
      value: 'email',
    },
    {
      label: 'name',
      value: 'userName',
    },
    {
      label: 'postRole',
      value: 'roleName',
    },
    {
      label: 'cancelApplyTime',
      value: 'createTime',
      isCancel: true,
    },
    {
      label: 'noUnfinishedOrder',
      value: 'noOrder',
    },
    {
      label: 'noUnfinishedClue',
      value: 'noClue',
    },
  ];
  const auditedInfo = [
    {
      label: 'cancelAuditTimeText',
      value: 'approvalTime',
    },
    {
      label: 'auditMethod',
      value: 'approvalMethod',
      format: (val: string) => getLabelFromDict(val, AuditWayOptions),
    },
    {
      label: 'auditPerson',
      value: 'createBy',
    },
    {
      label: 'auditResult',
      value: 'auditResult',
      format: (val: string) => getLabelFromDict(val, AuditStatusOptions),
    },
    {
      label: 'auditExplain',
      value: 'auditRemark',
    },
  ];
  let detail: any = reactive({});

  const emits = defineEmits(['success']);
  const formSchemas: FormSchema[] = [
    {
      label: t('auditResult'),
      field: 'auditResult',
      labelWidth: '100%',
      component: 'RadioGroup',
      required: true,
      componentProps: {
        options: AuditStatusOptions,
      },
    },
    {
      label: t('auditExplain'),
      field: 'auditRemark',
      labelWidth: '100%',
      component: 'InputTextArea',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        maxlength: 255,
      },
    },
  ];

  const [registerForm, { validate }] = useForm({
    schemas: formSchemas,
    showResetButton: false,
    showCancelButton: false,
    showSubmitButton: false,
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
  });
  const attrs = useAttrs();

  const [register, { closeModal }] = useModalInner(async (data) => {
    pageType.value = data.type;
    detail = data;
    if (data.type === 'view' && data.status !== AuditResultEnum.WAIT) {
      auditInfoAll.value = [...auditInfo, ...auditedInfo];
    } else {
      auditInfoAll.value = [...auditInfo];
    }
    const clueRes = await featchtUncompletedClue({
      userId: data.userId,
    });
    data.noClue = clueRes?.data?.IsFinish ? t('correct') : t('deny');

    const orderRes = await featchtUncompletedOrder({ userId: data.userId });
    console.log('res', orderRes.data);
    data.noOrder = orderRes?.data?.IsFinish ? t('correct') : t('deny');
  });

  /** 关闭弹窗 */
  const handleClose = () => {
    closeModal();
  };

  const handleOk = () => {
    validate().then(async (data) => {
      const [err] = await awaitTo(
        featchCancelUserAudit({
          auditRecordId: detail?.id || '',
          ...data,
        })
      );
      if (!err) {
        success(t('operationSuccess'));
        emits('success');
        closeModal();
      }
    });
  };
</script>

<style scoped>
  .red_text {
    color: red;
    margin-left: 20px;
  }
  .audit_modal {
    margin-bottom: 20px;
  }
</style>
