<template>
  <div class="box_container">
    <nav-bar :title="t('personal_info')" :left-arrow="true" :fixed="true" @click-left="goBack()" />

    <div class="personal_center_info">
      <div class="personal_center_info_item" style="margin-top: 10px">
        <div class="personal_center_info_item_title">
          {{ t('name') }}
        </div>
        <div class="personal_center_info_item_content">
          {{ userStore.getUserInfo.username }}
        </div>
      </div>
      <div class="personal_center_info_item" style="margin-top: 10px">
        <div class="personal_center_info_item_title">
          {{ t('binding_email') }}
        </div>
        <div class="personal_center_info_item_content">
          {{ userStore.getUserInfo.email }}
        </div>
      </div>
      <div class="personal_center_info_item" style="margin-top: 10px">
        <div class="personal_center_info_item_title">
          {{ t('country') }}
        </div>
        <div class="personal_center_info_item_content">
          {{ userStore.getUserInfo.countryName }}
        </div>
      </div>
      <div class="personal_center_info_item" style="margin-top: 10px">
        <div class="personal_center_info_item_title">
          {{ t('network_text') }}
        </div>
        <div class="personal_center_info_item_content"> {{ networkNames }} </div>
      </div>
      <div class="personal_center_info_item" style="margin-top: 10px">
        <div class="personal_center_info_item_title">
          {{ t('role_text') }}
        </div>
        <div class="personal_center_info_item_content">
          {{ userStore.getUserInfo.roleName }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="h5PersonalInfo">
  import { computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useUserStore } from '/@/store/modules/user';
  import { NavBar } from 'vant';
  import 'vant/lib/index.css';

  const { t } = useI18n('common');

  const router = useRouter();

  const userStore = useUserStore();

  const networkNames = computed(() => {
    // @ts-ignore
    const accountCategory = userStore.getUserInfo.accountCategory;
    return userStore.getUserInfo.networkList
      ?.filter((filterItems) => filterItems.type == accountCategory)
      ?.map((item) => item.networkName)
      .join(',');
  });

  const goBack = () => {
    router.back();
  };
</script>
<style scoped lang="less">
  .personal_center_info {
    margin-top: 56px;
    .personal_center_info_item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
    }
    .personal_center_info_item_content {
      flex: 1;
      text-align: right;
    }
  }

  ::v-deep(.van-nav-bar .van-icon) {
    color: #000000;
  }
</style>
