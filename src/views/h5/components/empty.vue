<template>
  <div class="data_empty">{{ props.text || t('emptyData') }}</div>
</template>
<script lang="ts" setup name="Empty">
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n('common');

  const props = defineProps({
    text: {
      type: String,
      default: '',
    },
  });
</script>
<script lang="ts">
  export default {
    name: 'Empty',
  };
</script>
<style scoped lang="less">
  .data_empty {
    width: 100%;
    height: 158px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #999;
  }
</style>
