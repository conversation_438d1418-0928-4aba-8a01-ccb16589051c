<template>
  <div class="docx-viewer-container">
    <nav-bar :title="viewDocType == 1 ? t('userAgreement') : t('privacy_policy')" :left-arrow="true" :fixed="true" @click-left="goBack()" />

    <div class="docx-viewer-content">
      <div v-if="loading">{{ t('loadingText') }}</div>
      <div v-else-if="error" class="error">{{ error }}</div>
      <div v-else v-html="docxHtml" class="docx-html"></div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, unref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { NavBar } from 'vant';
  import 'vant/lib/index.css';
  import { useI18n } from '/@/hooks/web/useI18n';
  import mammoth from 'mammoth';
  import { useLocale } from '/@/locales/useLocale';

  const { t } = useI18n('common');

  const { getLocale } = useLocale();
  const locale = unref(getLocale).split('-')[0];

  const router = useRouter();
  const route = useRoute();

  /**
   * 查看的文件类型
   * 1=用户协议
   * 2=隐私政策
   */
  const viewDocType = ref(route?.query?.viewDocType ? Number(route?.query?.viewDocType) : 1);

  const docList = ref({});
  const docxHtml = ref('');
  const loading = ref(false);
  const error = ref('');

  async function fetchDocList() {
    try {
      const res = await fetch('/doc/list.json');
      docList.value = await res.json();
    } catch (e) {
      error.value = t('downloadFailed');
    }
  }

  async function loadDocx() {
    docxHtml.value = '';
    error.value = '';
    if (!viewDocType.value || !locale) return;
    const fileName = docList.value[viewDocType.value == 1 ? 'userAgreement' : 'privacyPolicy'][locale];
    if (!fileName || !fileName.endsWith('.docx')) {
      error.value = '仅支持 docx 文件在线预览';
      return;
    }
    loading.value = true;
    try {
      const response = await fetch(`/doc/${fileName}`);
      if (!response.ok) throw new Error('文件加载失败');
      const arrayBuffer = await response.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      docxHtml.value = result.value;
    } catch (e) {
      error.value = t('downloadFailed');
    } finally {
      loading.value = false;
    }
  }

  const goBack = () => {
    router.back();
  };

  onMounted(async () => {
    await fetchDocList();

    loadDocx();
  });
</script>

<style scoped>
  .docx-viewer-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 24px;
  }
  .docx-viewer-content {
    margin-top: 56px;
    min-height: 300px;
    text-align: center;
    border: 1px solid #eee;
    padding: 20px;
    background: #fafbfc;
    border-radius: 6px;
  }
  .docx-html {
    font-size: 16px;
    line-height: 1.7;
    color: #222;
  }
  .error {
    color: red;
  }
</style>
