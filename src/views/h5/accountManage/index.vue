<template>
  <div class="box_container">
    <div class="h5_head">
      <div class="flex">
        <Icon size="30px" name="arrow-left" @click="goBack" />
        <span class="title">{{ t('account_management') }}</span>
      </div>
      <div class="h5_head_right flex" @click="openNetpoint">
        <div class="h5_head_right_item">
          <span class="h5_head_right_item_title">{{ queryParams.networkName ? queryParams.networkName : t('changStore') }}</span>
          <Icon name="exchange" />
        </div>
      </div>
    </div>

    <div style="margin-top: 20px">
      <Empty :text="t('emptyInviteAccount')" v-if="invitationAccountList.length === 0" />
      <div v-else class="invitation_link_list">
        <div class="invitation_link_list_item" v-for="item in invitationAccountList" :key="item.id" @click="handleInvitationAccount(item)">
          <div class="item">
            <div class="item_title">{{ t('name') }}：</div>
            <div class="item_content">{{ item.username }}</div>
          </div>
          <div class="item">
            <div class="item_title">{{ t('network_text') }}：</div>
            <div class="item_content" v-if="accountCategory == '2'">{{ item.customerName ? item.customerName : '' }}</div>
            <div class="item_content" v-else-if="accountCategory == '3'">{{ item.networkName ? item.networkName : '' }}</div>
          </div>
          <div class="item">
            <div class="item_title">{{ t('role_text') }}：</div>
            <div class="item_content">{{ item.roleName }}</div>
          </div>
          <div class="item">
            <div class="item_title">{{ t('status') }}：</div>
            <div class="item_content" :style="{ color: item.statusColor }">{{ item.statusName }}</div>
          </div>
          <div class="look_detail">
            <Icon name="arrow" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 门店选择 -->
  <Popup :show="showNetpoint" :closeable="false" position="bottom" :style="{ height: '38%' }" @click-overlay="showNetpoint = false">
    <Picker :columns="columns" @cancel="showNetpoint = false" @confirm="confirmSubmit" />
  </Popup>
</template>
<script lang="ts" setup name="h5AccountManage">
  import { ref, onMounted, defineComponent, computed, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useUserStore } from '/@/store/modules/user';
  import Empty from '/@/views/h5/components/empty.vue';
  import { querListByUserNetworkApi } from '/@/api/h5Api/loginXiangguan';
  import { queryStoreUserListApi } from '/@/api/h5Api/workTai';
  import { Picker, Popup, Icon } from 'vant';

  defineComponent({
    components: {
      Picker,
      Popup,
      Icon,
    },
  });

  const { t } = useI18n('common');

  const router = useRouter();

  /**
   * 账户类别 1厂端、2总代与子公司、3网点
   * */
  const accountCategory = computed(() => {
    // @ts-ignore
    return userStore.getUserInfo?.accountCategory;
  });

  const userStore = useUserStore();

  // 选择门店（网点）弹窗
  const showNetpoint = ref(false);
  const columns = ref([] as any[]);
  const confirmSubmit = (params: any) => {
    // 全部选择中
    if (params.selectedOptions[0].value === '') {
      queryParams.value.networkCode = '';
      queryParams.value.networkName = t('all');
      queryParams.value.type = '';
      showNetpoint.value = false;
      initData();
      return;
    }
    if (params.selectedOptions[0].type === 2) {
      queryParams.value.type = 2;
    } else if (params.selectedOptions[0].type === 3) {
      queryParams.value.type = 3;
    }
    queryParams.value.networkCode = params.selectedOptions[0].value;
    queryParams.value.networkName = params.selectedOptions[0].text;
    showNetpoint.value = false;
    initData();
  };

  // 邀请账户列表
  const invitationAccountList = ref([] as any[]);

  // 查询邀请账户列表参数
  const queryParams = ref({
    pageIndex: 1, // 页码
    pageSize: 1000, // 每页条数
    networkCode: '', // 网点编码
    networkList: [] as any[],
    customerList: [] as any[],
    networkName: t('all'),
    type: 2,
  } as any);

  const goBack = () => {
    const currentRoute = unref(router.currentRoute);
    const backUrl = (currentRoute.query.backUrl || '') as string;
    if (backUrl) {
      router.push({
        path: backUrl,
      });
      return;
    }
    router.back();
  };

  // 初始化数据
  const initData = async () => {
    try {
      let params = {
        networkList: [] as any[],
        customerList: [] as any[],
      };
      if (!queryParams.value?.type) {
        // 全部时候需要将columns.value字段赋值给params，type=2时赋值networkList，type=3时赋值customerList
        columns.value.forEach((item: any) => {
          if (item.type === 3) {
            params.networkList.push(item.value);
          } else if (item.type === 2) {
            params.customerList.push(item.value);
          }
        });
      } else {
        // 当前queryParams.value.type=3，则赋值networkList，type=2时赋值customerList,其他情况不赋值
        if (queryParams.value.type === 3) {
          params.networkList = [queryParams.value.networkCode];
        } else if (queryParams.value.type === 2) {
          params.customerList = [queryParams.value.networkCode];
        }
      }
      const res = await queryStoreUserListApi({
        ...queryParams.value,
        ...params,
      });
      invitationAccountList.value = ((res || []) as any[]).map((mapItems) => {
        let statusName = '';
        let statusColor = '#1989fa';
        switch (mapItems.status) {
          case 2:
            statusName = t('disableText');
            statusColor = '#f12727';
            break;
          case 1:
            statusName = t('enableText');
            break;
          case 4:
            statusName = t('managementAudit');
            break;
          case 5:
            statusName = t('cancellationSuccess');
            break;
        }
        return {
          ...mapItems,
          statusName,
          statusColor,
        };
      });
    } catch (err) {
      invitationAccountList.value = [];
    }
  };

  // 邀请账户查看
  const handleInvitationAccount = (item: any) => {
    router.push({
      path: '/mobile/h5EditAccount',
      query: {
        userId: item.userId,
      },
    });
  };

  const openNetpoint = () => {
    if (!columns.value.length) {
      // 可以加个 loading 或提示
      return;
    }
    showNetpoint.value = true;
  };

  onMounted(() => {
    querListByUserNetworkApi({ userId: userStore.getUserInfo.userId }).then((res) => {
      // 增加如果res只有一条数据或者不存在数据无需添加全部
      if (res.length > 1) {
        columns.value = [
          {
            text: t('all'),
            value: '',
          },
        ];
      }
      columns.value = [
        ...columns.value,
        ...res
          ?.filter((filterItems) => {
            return accountCategory.value == filterItems.type;
          })
          .map((mapItems) => {
            return {
              text: mapItems.networkName,
              value: mapItems.networkCode,
              type: mapItems.type,
            };
          }),
      ];
      /**
       * 如果columns.value的长度大于1条:networkName=t('all')
       * 如果=一条，取第一条的label设置给networkName
       * */
      if (columns.value.length > 1) {
        queryParams.value.networkName = t('all');
        queryParams.value.networkCode = '';
      } else if (columns.value.length == 1) {
        queryParams.value.networkName = columns.value[0].text;
        queryParams.value.networkCode = columns.value[0].value;
      }
      queryParams.value.type = '';
      initData();
    });
  });
</script>
<style scoped lang="less">
  .h5_head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      margin-left: 10px;
      font-size: 20px;
      font-weight: bolder;
    }
    .h5_head_right {
      display: flex;
      align-items: center;
      .h5_head_right_item {
        font-size: 14px;
        display: flex;
        align-items: center;
        .h5_head_right_item_title {
          text-align: right;
          width: 100px;
          margin-right: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .yanqing_manage {
    align-items: center;
    .yanqing_manage_title {
      font-size: 14px;
      font-weight: bolder;
    }
  }

  .invitation_link_list_item {
    position: relative;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    padding: 10px 30px 10px 10px;
    margin-bottom: 10px;
    .item {
      display: flex;
      justify-content: space-between;
    }
    .item_content {
      color: #999;
      flex: 1;
    }
    .look_detail {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }
  ::v-deep(.van-popup__close-icon--top-right) {
    top: 10px;
  }
</style>
