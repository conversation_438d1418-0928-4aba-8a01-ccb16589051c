<template>
  <div class="box_container">
    <div class="h5_head">
      <span>{{ t('workbenches_text') }}</span>
    </div>

    <div class="works" style="margin-top: 36px">
      <div class="work_every" @click="handleWorkEvery('/gac_iop_childapp/work-order-list-h5')" v-auth="'workOrderTodoH5'">
        <Icon size="30px" name="orders-o" />
        <div>{{ t('work_order_management') }}</div>
      </div>
      <div class="work_every" @click="handleWorkEvery('/gac_iop_childapp/clue-list-h5')" v-auth="'clueTodoH5'">
        <Icon size="30px" name="notes-o" />
        <div>{{ t('clue_management') }}</div>
      </div>
      <div class="work_every" @click="handleWorkEvery('h5InvitationLinkManagement')" v-auth="'h5InvitationLinkManagement'">
        <Icon size="30px" name="comment" />
        <div>{{ t('invitation_link_management') }}</div>
      </div>
      <div class="work_every" @click="handleWorkEvery('h5AccountManage')" v-auth="'h5-accountManage'">
        <Icon size="30px" name="user" />
        <div>{{ t('account_management') }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="h5Workbenches">
  import { useRouter } from 'vue-router';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Icon } from 'vant';
  import { onMounted } from 'vue';

  const { t } = useI18n('common');
  const router = useRouter();

  const handleWorkEvery = (path: string) => {
    let params = {};
    if (path === '/gac_iop_childapp/work-order-list-h5') {
      params = {
        active: -1,
        isAll: true,
        type: 2,
      };
    }
    router.push({
      path,
      query: params,
    });
  };

  onMounted(() => {
    console.log('workBaches onMounted ................');
  });
</script>
<style scoped lang="less">
  .h5_head {
    display: flex;
    align-items: center;
    span {
      margin-left: 10px;
      font-size: 20px;
      font-weight: bolder;
    }
  }

  .works {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  .work_every {
    width: 49%;
    border: 2px solid #f8f8f8;
    text-align: center;
    padding: 20px 0;
    border-radius: 10px;
    margin-bottom: 10px;
  }
</style>
