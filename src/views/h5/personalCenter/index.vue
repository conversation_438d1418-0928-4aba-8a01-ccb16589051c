<template>
  <div class="common-page box_container">
    <nav-bar :title="t('personalCenter')" :left-arrow="false" :fixed="true" />

    <div class="personal_center_info">
      <div class="personal_center_info_haed flex">
        <!-- 头像 -->
        <div class="personal_center_info_haed_avatar">
          <Image width="62" height="62" :round="true" :src="logoImg" />
        </div>
        <!-- 姓名 -->
        <div class="personal_center_info_haed_name">
          <div class="username">{{ userStore.getUserInfo.username }}</div>
          <div class="role"
            ><span>{{ t('role_text') }}：</span>{{ userStore.getUserInfo.roleName }}</div
          >
          <div class="network">
            <span>{{ t('network_text') }}：</span>{{ networkNames }}</div
          >
        </div>
      </div>
      <!-- 每项 -->
      <div class="personal_center_info_item" style="margin-top: 10px" @click="toPersonalInfo()" v-auth="'h5PersonalInfo'">
        <div class="personal_center_info_item_title">
          {{ t('personal_info') }}
        </div>
        <div class="personal_center_info_item_content">
          <Icon name="arrow" />
        </div>
      </div>
      <div class="personal_center_info_item" @click="handleShenqingLogout()" v-auth="'h5ResignationAccountCancellation'">
        <div class="personal_center_info_item_title">
          {{ t('resignAccountCancellation') }}
        </div>
        <div class="personal_center_info_item_content">
          <span style="color: #1990ff; margin-right: 10px" v-if="zhuxiaoAuditInfo?.status === 1">{{ t('cancellationAudit') }}</span>
          <span style="color: #f12727; margin-right: 10px" v-else-if="zhuxiaoAuditInfo?.status === 3 && zhuxiaoAuditInfo?.auditResult == 2">{{
            t('cancellationAuditNotPass')
          }}</span>
          <Icon name="arrow" />
        </div>
      </div>
      <div class="personal_center_info_item" @click="handleLogout()" v-auth="'h5LogOut'">
        <div class="personal_center_info_item_title">
          {{ t('logout_button') }}
        </div>
        <div class="personal_center_info_item_content">
          <Icon name="arrow" />
        </div>
      </div>
    </div>
  </div>

  <!-- <Tabbar /> -->

  <!-- 注销申请须知 -->
  <Popup
    :show="showApplyLogOff"
    closeable
    position="center"
    :style="{ width: '86%', minHeight: '50%' }"
    @click-overlay="showApplyLogOff = false"
    @close="showApplyLogOff = false"
  >
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popup_select_head_left">
          <text>{{ t('log_off_instructions') }}</text>
        </div>
      </div>
      <div class="popup_select_content">
        <div class="popup_select_content_item">
          <div class="popup_select_content_item_title">
            <div>{{ t('account_cancel_instructions') }}</div>
            <div style="margin-top: 10px">{{ `${t('cancellationSuccess')}, ${t('deleteMsg')}` }}</div>
          </div>
          <div class="not_end_order" style="margin-top: 10px">
            <span style="margin-right: 10px">{{ t('noUnfinishedOrder') }}</span>
            <Icon color="#1990ff" name="success" v-if="notEndOrder" />
            <Icon color="#f12727" name="cross" v-else />
          </div>
          <div class="not_end_order" style="margin-top: 10px">
            <span style="margin-right: 10px">{{ t('noUnfinishedClue') }}</span>
            <Icon color="#1990ff" name="success" v-if="notEndClue" />
            <Icon color="#f12727" name="cross" v-else />
          </div>
          <div class="not_end_order" style="margin-top: 30px" v-if="!notEndOrder || !notEndClue">
            <span>{{ t('pleaseCompleteRelatedTasksBeforeResubmittingCancellation') }}</span>
          </div>
        </div>

        <Button style="margin-top: 20px; margin-bottom: 10px" type="primary" :disabled="!notEndOrder || !notEndClue" @click="gotoZhuxiao()">{{
          t('immediatelyCancel')
        }}</Button>
      </div>
    </div>
  </Popup>

  <!-- 验证邮箱用以注销 -->
  <Popup
    :show="showVerifyEmail"
    closeable
    position="center"
    :style="{ width: '80%', height: '40%' }"
    @click-overlay="showVerifyEmail = false"
    @close="showVerifyEmail = false"
  >
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popup_select_head_left">
          <text>{{ t('verifyEmailForCancellation') }}</text>
        </div>
      </div>
      <div class="not_end_order" style="margin-top: 10px; padding: 0 16px">
        <span style="margin-right: 10px">{{ t('email') }}：</span>
        <span>{{ userStore.getUserInfo.email }}</span>
      </div>
      <div class="input_box">
        <div class="input_item">
          <div class="input_item_label">
            <span>{{ t('inputCode') }}</span>
          </div>
          <div class="input_item_input send_sms flex">
            <input type="text" v-model="formData.code" autocomplete="off" />
            <Button type="primary" v-if="!isShowDjs" @click.stop="sendSms()">{{ t('send_verification_code') }}</Button>
            <count-down :time="djsTime" v-else @finish="isShowDjs = false">
              <template #default="timeData">
                <div class="djs_item">{{ timeData.seconds }}</div>
              </template>
            </count-down>
          </div>
        </div>
      </div>
      <div style="padding: 0 16px">
        <Button style="margin-top: 56px" type="primary" @click="applyZhuxiao()">{{ t('applyCancel') }}</Button>
      </div>
    </div>
  </Popup>

  <!-- 注销审核中 -->
  <Popup
    :show="showZhuxiaoAudit"
    closeable
    position="center"
    :style="{ width: '86%', minHeight: '36%' }"
    @click-overlay="showZhuxiaoAudit = false"
    @close="showZhuxiaoAudit = false"
  >
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popup_select_head_left">
          <text>{{ t('cancellationAudit') }}</text>
        </div>
      </div>
      <div class="popup_select_content">
        <div class="popup_select_content_item">
          <div class="not_end_order" style="margin-top: 10px">
            <span style="margin-right: 10px">{{ t('cancellationCountdown') }}：</span>
            <span>{{ formatMinutesToHourMinute(zhuxiaoAuditInfo?.approvalApprovalTime || 0, t) }}</span>
          </div>
          <div class="not_end_order" style="margin-top: 10px">
            <span style="margin-right: 10px">{{ t('cancellationSuccess') }}：</span>
            <span>{{ t('deleteMsg') }}</span>
          </div>
          <div class="not_end_order" style="margin-top: 10px">
            <span style="margin-right: 10px">{{ t('cancelTime') }}：</span>
            <span>{{ zhuxiaoAuditInfo?.createTime ? dayjs(zhuxiaoAuditInfo.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</span>
          </div>
        </div>
      </div>
    </div>
  </Popup>

  <!-- 注销审核不通过 -->
  <Popup
    :show="showZhuxiaoAuditNotPass"
    closeable
    position="center"
    :style="{ width: '86%', minHeight: '40%' }"
    @click-overlay="showZhuxiaoAuditNotPass = false"
    @close="showZhuxiaoAuditNotPass = false"
  >
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popup_select_head_left">
          <text>{{ t('cancellationAuditNotPass') }}</text>
        </div>
      </div>
      <div class="popup_select_content">
        <div class="popup_select_content_item">
          <div class="not_end_order" style="margin-top: 10px">
            <div style="margin-right: 10px">{{ t('notPassReason') }}</div>
            <div>{{ zhuxiaoAuditInfo?.auditRemark }}</div>
          </div>
        </div>
        <div style="width: 100%; margin-top: 36px; margin-bottom: 10px; display: flex; justify-content: space-between">
          <Button style="width: 120px" type="primary" @click="againApplyApplyZhuxiao()">{{ t('reapply') }}</Button>
          <Button style="width: 120px" @click="showZhuxiaoAuditNotPass = false">{{ t('cancelApplication') }}</Button>
        </div>
      </div>
    </div>
  </Popup>
</template>
<script lang="ts" setup name="h5PersonalCenter">
  import { ref, onMounted, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import dayjs from 'dayjs';
  import { formatMinutesToHourMinute } from '/@/utils';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useUserStore } from '/@/store/modules/user';
  import { showToast, showDialog, CountDown, NavBar, Popup, Button, Icon, Image } from 'vant';
  import logoImg from '/@/assets/images/logo.png';
  import { getZhuxiaoAuditInfoApi, initiateCancellationApi, sendZhuxiaoCodeApi } from '/@/api/h5Api/zhuxiao';
  import { getOrderInfoByUserIdApi, getUncompletedClueByUserIdApi } from '/@/api/userCenter';
  const { t } = useI18n('common');

  const router = useRouter();

  const userStore = useUserStore();

  const networkNames = computed(() => {
    // @ts-ignore
    const accountCategory = userStore.getUserInfo.accountCategory;
    return userStore.getUserInfo.networkList
      ?.filter((filterItems) => filterItems.type == accountCategory)
      ?.map((item) => item.networkName)
      .join(',');
  });

  // 注销详情数据
  const zhuxiaoAuditInfo = ref(null as any);

  // 申请离职弹窗是否显示-注销申请须知
  const showApplyLogOff = ref(false);
  // 去立即注销-打开验证邮箱用以注销弹窗
  const gotoZhuxiao = () => {
    formData.value.code = '';
    showVerifyEmail.value = true;
  };

  // 验证邮箱用以注销弹窗是否显示
  const showVerifyEmail = ref(false);

  // 注销审核中弹窗是否显示
  const showZhuxiaoAudit = ref(false);

  // 注销审核不通过弹窗是否显示
  const showZhuxiaoAuditNotPass = ref(false);

  // 无未完结工单
  const notEndOrder = ref(false);
  // 无未完结线索
  const notEndClue = ref(false);

  // 个人信息
  const toPersonalInfo = () => {
    router.push('/mobile/h5PersonalInfo');
  };

  // 去申请离职
  const handleShenqingLogout = async () => {
    // 注销审核中
    if (zhuxiaoAuditInfo.value?.status === 1) {
      showZhuxiaoAudit.value = true;
      return;
    }
    // 注销审核不通过
    if (zhuxiaoAuditInfo.value?.status === 3 && zhuxiaoAuditInfo?.value.auditResult == 2) {
      showZhuxiaoAuditNotPass.value = true;
      return;
    }
    await getUncompletedOrderAndClue();
    showApplyLogOff.value = true;
  };

  // 重新发起注销
  const againApplyApplyZhuxiao = async () => {
    showZhuxiaoAuditNotPass.value = false;
    await getUncompletedOrderAndClue();
    showApplyLogOff.value = true;
  };

  // 退出登录
  const handleLogout = () => {
    handleLoginOut();
  };
  //  login out
  function handleLoginOut() {
    showDialog({
      message: t('app_logoutMessage'),
      confirmButtonText: t('logout_button'),
      showCancelButton: true,
      cancelButtonText: t('cancel'),
    }).then(async () => {
      userStore.logout(true);
    });
  }

  // 注销表单
  const formData = ref({
    code: '',
  });
  // 倒计时
  const djsTime = ref(60 * 1000);
  // 是否显示倒计时
  const isShowDjs = ref(false);
  const loading = ref(false);

  // 发送验证码
  const sendSms = async () => {
    if (loading.value) return;
    loading.value = true;
    try {
      await sendZhuxiaoCodeApi();
      showToast({ message: t('verification_code_sent_successfully') });
      isShowDjs.value = true;
      loading.value = false;
    } catch (err) {
      loading.value = false;
    }
  };
  // 申请注销
  const applyZhuxiao = async () => {
    if (!formData.value.code) {
      showToast({ message: t('inputCodePlaceholder') });
      return;
    }
    if (!notEndOrder.value) {
      // 您的工单为完结
      showToast({ message: t('yourOrderIsNotCompleted') });
      return;
    }
    if (!notEndClue.value) {
      // 您的线索为完结
      showToast({ message: t('yourClueIsNotCompleted') });
      return;
    }
    if (loading.value) return;
    loading.value = true;
    try {
      await initiateCancellationApi({
        code: formData.value.code,
      });
      showToast({ message: t('applyCancellationSuccess') });
      initData();
      showVerifyEmail.value = false;
      showApplyLogOff.value = false;
      loading.value = false;
    } catch (err) {
      loading.value = false;
    }
  };

  // 初始化数据
  const initData = async () => {
    try {
      const res = await getZhuxiaoAuditInfoApi({ userId: String(userStore.getUserInfo.userId) });
      zhuxiaoAuditInfo.value = res || null;
    } catch (err) {}
  };

  // 获取是否存在未完结工单、线索
  const getUncompletedOrderAndClue = async () => {
    try {
      const orderRes = await getOrderInfoByUserIdApi({ userId: String(userStore.getUserInfo.userId) });
      // @ts-ignore
      notEndOrder.value = orderRes.IsFinish;
    } catch (err) {}
    try {
      const uncompleteRes = await getUncompletedClueByUserIdApi({ userId: String(userStore.getUserInfo.userId) });
      notEndClue.value = uncompleteRes.IsFinish;
    } catch (err) {}
  };

  onMounted(async () => {
    initData();
  });
</script>
<style scoped lang="less">
  .personal_center_info {
    margin-top: 56px;
    .personal_center_info_haed {
      border-bottom: 1px solid #999999;
      padding-bottom: 20px;
    }
    .personal_center_info_haed_avatar {
      width: 62px;
      height: 62px;
      border-radius: 50%;
      overflow: hidden;
      border: 1px solid #999999;
    }
    .personal_center_info_haed_name {
      flex: 1;
      margin-left: 10px;
      .username {
        font-size: 20px;
        font-weight: 600;
        color: #000000;
      }
      .role,
      .network {
        font-size: 14px;
        color: #999999;
      }
    }

    .personal_center_info_item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
    }
  }

  .popup_select_box {
    width: 100%;
    height: 100%;
    .popup_select_head {
      width: 100%;
      height: 40px;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 50px 10px 16px;
      box-sizing: border-box;
      color: #000000;
      font-weight: bolder;
      .popup_select_head_left {
        line-height: 20px;
      }
    }
    .popup_select_content {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: calc(100% - 40px);
      overflow: auto;
      padding: 16px;
      box-sizing: border-box;
      .popup_select_content_item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  ::v-deep(.van-popup__close-icon--top-right) {
    top: 10px;
  }
  ::v-deep(.van-button) {
    width: 100%;
  }

  .input_box {
    padding: 0 16px;
    margin-top: 10px;
    .input_item {
      margin-bottom: 20px;
      .input_item_label {
        font-size: 14px;
        color: #333;
        margin-bottom: 10px;
      }
      .input_item_input {
        input {
          width: 100%;
          height: 40px;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          padding: 0 10px;
        }
      }
    }
  }

  .send_sms ::v-deep(.van-button) {
    width: 186px;
    margin-left: 10px;
  }

  .djs_item {
    width: 56px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    color: #fff;
    font-size: 12px;
    text-align: center;
    background-color: #1989fa;
    margin-left: 10px;
    border-radius: 4px;
  }
</style>
<style>
  .van-button--disabled {
    opacity: 0.2;
  }
</style>
