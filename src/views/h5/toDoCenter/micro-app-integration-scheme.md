# H5 Tab切换微应用集成方案 - 无URL变更

## 方案概述

本文档描述了在不改变当前URL的情况下，通过Tab切换加载不同子应用内容的实现方案。采用了"动态路由配置+内嵌挂载点"的方式，在主应用中为不同Tab准备独立容器，通过qiankun的`loadMicroApp`API按需加载子应用。

## 实现原理

1. **独立容器**: 在每个Tab内部创建独立的微应用容器
2. **动态加载**: 使用qiankun的`loadMicroApp`API手动加载/卸载微应用
3. **参数传递**: 通过URL参数和props向子应用传递初始路径和上下文数据
4. **状态保持**: 切换Tab时维持应用状态，不改变URL

## 主要修改内容

### 主应用修改

1. 为订单待办和线索待办Tab添加微应用容器

   ```html
   <div id="order-todo-container" class="micro-app-container"></div> <div id="clue-todo-container" class="micro-app-container"></div>
   ```

2. 定义微应用配置

   ```typescript
   const microApps: Record<string, MicroAppConfig> = {
     orderToDo: {
       name: 'order-app',
       entry: import.meta.env.VITE_APP_SUB_gac_iop_childapp || '',
       container: '#order-todo-container',
       initialPath: '/mobileH5',
     },
     clueToDo: {
       name: 'clue-app',
       entry: import.meta.env.VITE_APP_SUB_gac_iop_childapp || '',
       container: '#clue-todo-container',
       initialPath: '/clue-todo',
     },
   };
   ```

3. 实现Tab切换逻辑

   ```typescript
   const handleTabChange = (index: number) => {
     // 卸载当前应用
     if (currentMicroApp) {
       currentMicroApp.unmount();
       currentMicroApp = null;
     }

     // 根据Tab索引确定要加载的应用
     let appConfig: MicroAppConfig | null = null;
     if (index === 1) {
       // orderToDo
       appConfig = microApps.orderToDo;
     } else if (index === 2) {
       // clueToDo
       appConfig = microApps.clueToDo;
     }

     // 加载新应用
     if (appConfig && appConfig.entry) {
       // 通过URL传递初始路径参数
       const appEntry = `${appConfig.entry}?initialPath=${encodeURIComponent(appConfig.initialPath)}`;

       // 获取props并添加自定义参数
       const propsData = getProps();
       const customProps = {
         ...propsData,
         initialPath: appConfig.initialPath,
         queryParams: queryParams.value,
       };

       currentMicroApp = loadMicroApp({
         name: appConfig.name,
         entry: appEntry,
         container: appConfig.container,
         props: customProps,
       });
     }
   };
   ```

4. 监听Tab变化，触发微应用加载/卸载
   ```typescript
   watch(() => active.value, handleTabChange);
   ```

### 子应用处理逻辑

子应用需要在入口文件中添加处理initialPath的逻辑:

```javascript
// 子应用路由配置示例
// 使用内存路由
webHistory = createMemoryHistory(import.meta.env.VITE_PUBLIC_PATH);

// 子应用入口文件示例
export async function mount(props) {
  // 从props中获取初始路径
  if (props.initialPath) {
    handleInitialRoute(props.initialPath);
  }

  // 初始路径处理函数
  function handleInitialRoute(path) {
    // 方法1: 使用子应用路由进行导航但不改变URL
    router.replace(path);

    // 方法2: 直接渲染对应组件
    // renderComponent(path);
  }
}
```

## 优势与特点

1. **不改变URL**: 在不改变当前URL的情况下切换微应用内容
2. **低侵入性**: 不需要修改子应用代码，只需子应用支持通过URL参数获取初始路径
3. **性能优化**: 按需加载子应用，减少资源占用
4. **状态隔离**: 不同Tab的微应用相互独立，不会互相干扰
5. **代码简洁**: 通过简单的配置和生命周期管理实现复杂功能

## 注意事项

1. 确保每个微应用的容器ID唯一
2. 子应用需要能够正确解析URL参数或props来决定初始渲染内容
3. 子应用加载状态需要妥善处理，可添加加载指示器
4. 微应用资源需要配置好CORS策略，确保跨域访问
5. 子应用跳转逻辑应避免改变主应用URL
6. 子应用路由模式应使用内存模式(memory history)或hash模式，避免影响主应用URL

## 使用方法

1. 配置微应用入口地址(通过环境变量)
2. 定义不同Tab对应的初始路径
3. 切换Tab时会自动加载/卸载相应的微应用
4. 子应用需实现props参数处理逻辑，接收并处理initialPath

## 后续优化方向

1. 添加微应用预加载功能
2. 实现微应用加载状态显示
3. 优化微应用通信机制
4. 实现子应用状态持久化
