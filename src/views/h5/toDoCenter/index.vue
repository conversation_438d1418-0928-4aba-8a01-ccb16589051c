<template>
  <div class="">
    <div class="h5_head box_container">
      <div class="flex">
        <span class="title">{{ t('todo_center') }}</span>
      </div>
      <div class="h5_head_right flex" @click="openNetpoint">
        <div class="h5_head_right_item">
          <span class="h5_head_right_item_title">{{ queryParams.networkName ? queryParams.networkName : t('changStore') }}</span>
          <Icon name="exchange" />
        </div>
      </div>
    </div>

    <div class="h5_content">
      <!-- 使用Vant的Overlay和Loading组件 -->
      <Overlay :show="isLoadingMicroApp" :lock-scroll="false" class="transparent-overlay">
        <div class="loading-wrapper">
          <Loading type="spinner" size="24px" color="#1989fa" />
        </div>
      </Overlay>

      <Tabs :class="{ 'tabs-disabled': isLoadingMicroApp }" v-model="activeName" @change="handleTabChange">
        <Tab name="audit" :title="t('resignAccountCancellationAudit')" v-if="hasImportAuth('h5ResignationCancellationReview')">
          <AuditAccount ref="auditAccountRef" />
        </Tab>
        <Tab name="orderToDo" :title="t('orderToDo')" v-if="hasImportAuth('h5OrderToDo')">
          <div ref="orderToDoContainer" id="order-todo-container" class="micro-app-container"></div>
        </Tab>
        <Tab name="clueToDo" :title="t('clueToDo')" v-if="hasImportAuth('h5ClueToDo')">
          <div id="clue-todo-container" class="micro-app-container"></div>
        </Tab>
      </Tabs>
    </div>
  </div>

  <!-- 门店选择 -->
  <Popup :show="showNetpoint" :closeable="false" position="bottom" :style="{ height: '38%' }" @click-overlay="showNetpoint = false">
    <Picker :columns="columns" @cancel="showNetpoint = false" @confirm="confirmSubmit" />
  </Popup>
  <Popup :show="showNetpointOther" :closeable="false" position="bottom" :style="{ height: '38%' }" @click-overlay="showNetpointOther = false">
    <Picker :columns="columns.filter((filterItems) => !!filterItems.value)" @cancel="showNetpointOther = false" @confirm="confirmSubmit" />
  </Popup>
</template>
<script lang="ts" setup name="h5ToDoCenter">
  import { ref, onMounted, computed, defineComponent, watch, onUnmounted, nextTick } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useUserStore } from '/@/store/modules/user';
  import { Icon, Tabs, Tab, Popup, Picker, Loading, Overlay } from 'vant';
  import AuditAccount from './components/auditAccount.vue';
  import { querListByUserNetworkApi } from '/@/api/h5Api/loginXiangguan';
  import { loadMicroApp, MicroApp } from 'qiankun';
  import { getProps } from '/@/qiankun/state';
  import { switchStore } from '/@/api/sys/user';

  defineComponent({
    components: {
      Icon,
      Popup,
      Picker,
    },
  });

  const { t } = useI18n('common');

  //按钮权限问题
  const { hasPermission } = usePermission();
  // 操作权限
  function hasImportAuth(auth) {
    return hasPermission(auth);
  }

  const orderToDoContainer = ref();
  /**
   * 账户类别 1厂端、2总代与子公司、3网点
   * */
  const accountCategory = computed(() => {
    // @ts-ignore
    return userStore.getUserInfo?.accountCategory;
  });

  // tab 切换
  const activeName = ref('audit' as string);

  // 微应用加载状态管理
  const isLoadingMicroApp = ref(false);
  const lastActiveTab = ref('');

  // 查询邀请链接列表参数
  const queryParams = ref({
    networkList: [] as any[],
    customerList: [] as any[],
    networkName: '', // 网点名称
  });

  // 离职审核ref
  const auditAccountRef = ref();

  // 选择门店（网点）弹窗
  const showNetpoint = ref(false);
  const showNetpointOther = ref(false);

  const columns = ref([] as any[]);
  const confirmSubmit = (params: any) => {
    localStorage.setItem('SELECT_STATE_TAB', activeName.value);
    // 全部选择中
    if (params.selectedOptions[0].value === '') {
      queryParams.value.networkName = t('all');
      showNetpoint.value = false;
      showNetpointOther.value = false;
      let params = {
        networkList: [] as any[],
        customerList: [] as any[],
      };
      // 全部时候需要将columns.value字段赋值给params，type=2时赋值networkList，type=3时赋值customerList
      columns.value.forEach((item: any) => {
        if (item.type === 3) {
          params.networkList.push(item.value);
        } else if (item.type === 2) {
          params.customerList.push(item.value);
        }
      });
      // 离职审核列表页面刷新数据
      if (activeName.value === 'audit') {
        auditAccountRef.value?.initFn({
          ...queryParams.value,
          ...params,
        });
      }
      return;
    }
    // 当前的选中tab不等于离职注销审核，需要刷新页面
    if (activeName.value !== 'audit') {
      switchStore({ networkCode: params.selectedOptions[0].value }).then(() => {
        // 设置当前选中的tab到本地缓存
        const userinfo = userStore.getUserInfo;
        userStore.setUserInfo({
          ...(userinfo || {}),
          networkCode: params.selectedOptions[0].value,
          networkName: params.selectedOptions[0].text,
        });
        location.reload();
      });
      return;
    }

    // 选中网点或者子公司
    if (params.selectedOptions[0].type === 2) {
      queryParams.value.networkList = [];
      queryParams.value.customerList = [params.selectedOptions[0].value];
    } else if (params.selectedOptions[0].type === 3) {
      queryParams.value.customerList = [];
      queryParams.value.networkList = [params.selectedOptions[0].value];
    }
    queryParams.value.networkName = params.selectedOptions[0].text;
    showNetpoint.value = false;
    showNetpointOther.value = false;
    // 离职审核列表页面刷新数据
    if (activeName.value === 'audit') {
      auditAccountRef.value?.initFn(queryParams.value);
    }
    // 切换第一个或者第二个-更新子应用，重新传值。
    // @ts-ignore
    if (activeName.value === 'orderToDo' || activeName.value === 'clueToDo') {
      initMicroAppFn(activeName.value);
    }
  };
  const openNetpoint = () => {
    console.log(columns.value);
    if (!columns.value.length) {
      // 可以加个 loading 或提示
      return;
    }
    if (activeName.value === 'audit') {
      showNetpoint.value = true;
    } else {
      /**
       * 默认选中数据
       * 根据当前用户信息的networkCode默认选中
       * 设置选中的默认值
       */

      showNetpointOther.value = true;
    }
  };

  const userStore = useUserStore();

  // 定义微应用配置的接口
  interface MicroAppConfig {
    name: string;
    entry: string;
    container: string;
    initialPath: string;
  }

  // 微应用配置
  const microApps: Record<string, MicroAppConfig> = {
    orderToDo: {
      name: 'order-app',
      entry: import.meta.env.VITE_APP_SUB_gac_iop_childapp || '',
      container: '#order-todo-container',
      initialPath: '/gac_iop_childapp/work-order-todo-h5',
      // initialPath: '/demo',
    },
    clueToDo: {
      name: 'clue-app',
      entry: import.meta.env.VITE_APP_SUB_gac_iop_childapp || '',
      container: '#clue-todo-container',
      initialPath: '/gac_iop_childapp/clue-todo-h5',
    },
  };

  // 当前激活的微应用实例
  let currentMicroApp: MicroApp | null = null;

  // 初始化子应用以及传值
  async function initMicroAppFn(type: string) {
    // 防止重复加载
    if (isLoadingMicroApp.value) {
      console.log('微应用正在加载中，跳过重复请求');
      return;
    }

    try {
      isLoadingMicroApp.value = true;

      await nextTick();
      console.log('orderToDoContainer', orderToDoContainer.value);

      // 卸载当前应用
      if (currentMicroApp) {
        console.log('正在卸载当前微应用...');
        await currentMicroApp.unmount();
        currentMicroApp = null;
        // 等待一小段时间确保完全卸载
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      const appConfig = microApps[type];
      // 加载新应用
      if (appConfig && appConfig.entry) {
        console.log(`开始加载微应用: ${type}`);

        // 通过URL传递初始路径参数
        const appEntry = `${appConfig.entry}?initialPath=${encodeURIComponent(appConfig.initialPath)}`;
        // 获取props并添加自定义参数
        const propsData = getProps();
        // 扩展props，添加自定义属性
        const customProps = {
          ...propsData,
          initialPath: appConfig.initialPath,
          tabLoadChild: true, // 标记子应用以tab方式加载
          queryParams: queryParams.value,
        };

        currentMicroApp = loadMicroApp({
          name: appConfig.name,
          entry: appEntry,
          container: appConfig.container,
          props: customProps,
        });

        console.log('微应用加载完成:', currentMicroApp);
        lastActiveTab.value = type;
      }
    } catch (error) {
      console.error('微应用加载失败:', error);
      // 可以在这里添加错误提示给用户
    } finally {
      isLoadingMicroApp.value = false;
    }
  }

  // 处理Tab切换 - 严格控制加载状态
  const handleTabChange = (type: string) => {
    // 如果正在加载微应用，阻止切换
    if (isLoadingMicroApp.value) {
      console.log('微应用正在加载中，禁止切换Tab');
      // 恢复到之前的tab
      activeName.value = lastActiveTab.value;
      return;
    }

    // 如果是相同的tab，直接返回
    if (lastActiveTab.value === type) {
      return;
    }

    console.log(`切换到Tab: ${type}`);
    activeName.value = type;

    if (type === 'audit') {
      auditAccountRef.value?.initFn(queryParams.value);
      lastActiveTab.value = type;
      return;
    }

    /**
     * 默认选中数据
     * 根据当前用户信息的networkCode默认选中
     * 设置选中的默认值
     */
    const networkCodeEnd = userStore.getUserInfo.networkCode;
    const findParams = columns.value.find((findItems) => findItems.value == networkCodeEnd) || columns.value[0];
    if (!!findParams) {
      // 设置选中的值的name
      queryParams.value.networkName = findParams?.text;
      // 选中网点或者子公司
      if (findParams.type === 2) {
        queryParams.value.networkList = [];
        queryParams.value.customerList = [findParams.value];
      } else if (findParams.type === 3) {
        queryParams.value.customerList = [];
        queryParams.value.networkList = [findParams.value];
      }
    }

    if (type === 'orderToDo') {
      initMicroAppFn('orderToDo');
    } else if (type === 'clueToDo') {
      initMicroAppFn('clueToDo');
    }
  };

  // const router = useRouter();
  onMounted(() => {
    console.log('onMounted');
    querListByUserNetworkApi({ userId: userStore.getUserInfo.userId }).then((res) => {
      // 增加如果res只有一条数据或者不存在数据无需添加全部
      if (res.length > 1) {
        columns.value = [
          {
            text: t('all'),
            value: '',
          },
        ];
      }
      columns.value = [
        ...columns.value,
        ...res
          ?.filter((filterItems) => {
            return accountCategory.value == filterItems.type;
          })
          ?.map((mapItems) => {
            return {
              text: mapItems.networkName,
              value: mapItems.networkCode,
              type: mapItems.type,
            };
          }),
      ];
      /**
       * 如果columns.value的长度大于1条:networkName=t('all')
       * 如果=一条，取第一条的label设置给networkName
       * */
      if (columns.value.length > 1) {
        queryParams.value.networkName = t('all');
      } else if (columns.value.length == 1) {
        queryParams.value.networkName = columns.value[0].text;
      }
      // 全部时候需要将columns.value字段赋值给params，type=2时赋值networkList，type=3时赋值customerList
      columns.value.forEach((item: any) => {
        if (item.type === 3) {
          queryParams.value.networkList.push(item.value);
        } else if (item.type === 2) {
          queryParams.value.customerList.push(item.value);
        }
      });
      // 默认选中的值
      // const selectTab = localStorage.getItem('SELECT_STATE_TAB');

      let hasAuth: any[] = [];
      if (hasImportAuth('h5ResignationCancellationReview')) {
        hasAuth.push('audit');
      }
      if (hasImportAuth('h5OrderToDo')) {
        hasAuth.push('orderToDo');
      }
      if (hasImportAuth('h5ClueToDo')) {
        hasAuth.push('clueToDo');
      }
      // 初始化-验针tab权限值
      // if (!selectTab) {
      if (!hasImportAuth('h5ResignationCancellationReview')) {
        /**
         * 默认选中数据
         * 根据当前用户信息的networkCode默认选中
         * 设置选中的默认值
         */
        const networkCodeEnd = userStore.getUserInfo.networkCode;
        const findParams = columns.value.find((findItems) => findItems.value == networkCodeEnd) || columns.value[0];
        if (!!findParams) {
          // 设置选中的值的name
          queryParams.value.networkName = findParams?.text;
          // 选中网点或者子公司
          if (findParams.type === 2) {
            queryParams.value.networkList = [];
            queryParams.value.customerList = [findParams.value];
          } else if (findParams.type === 3) {
            queryParams.value.customerList = [];
            queryParams.value.networkList = [findParams.value];
          }
        }
        /**
         * 判定当前页面是否存在工单待办
         * 如果存在，则初始化工单待办的子应用
         * 如果不存在+存在线索待办TAB权限，则初始化线索待办子应用
         * 如果工单和线索都不存在则都不初始化
         * */
        if (hasImportAuth('h5OrderToDo')) {
          activeName.value = 'orderToDo';
          lastActiveTab.value = 'orderToDo';
          initMicroAppFn('orderToDo');
        } else if (hasImportAuth('h5ClueToDo')) {
          activeName.value = 'clueToDo';
          lastActiveTab.value = 'clueToDo';
          initMicroAppFn('clueToDo');
        }
      } else {
        // 如何本地
        activeName.value = 'audit';
        lastActiveTab.value = 'audit';
        auditAccountRef.value?.initFn(queryParams.value);
      }
      // }
      // if (selectTab && hasAuth.includes(selectTab)) {
      //   activeName.value = selectTab;
      //   if (activeName.value === 'audit') {
      //     auditAccountRef.value?.initFn(queryParams.value);
      //   } else if (activeName.value === 'orderToDo') {
      //     initMicroAppFn('orderToDo');
      //   } else if (activeName.value === 'clueToDo') {
      //     initMicroAppFn('clueToDo');
      //   }
      // }
      // console.log(selectTab);
    });
  });

  // 卸载微应用
  onUnmounted(() => {
    // 卸载微应用
    if (currentMicroApp) {
      currentMicroApp.unmount();
      currentMicroApp = null;
    }
  });

  // 监听Tab变化
  // watch(() => activeName.value, handleTabChange);
</script>
<style scoped lang="less">
  .h5_head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      margin-left: 10px;
      font-size: 20px;
      font-weight: bolder;
    }
    .h5_head_right {
      display: flex;
      align-items: center;
      .h5_head_right_item {
        font-size: 14px;
        display: flex;
        align-items: center;
        .h5_head_right_item_title {
          width: 100px;
          text-align: right;
          margin-right: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .h5_content {
    width: 100%;
    height: calc(100vh - 120px);
    position: relative;

    ::v-deep(.van-tabs) {
      height: 100%;
    }
    ::v-deep(.van-tabs__content) {
      height: calc(100% - 44px);
      overflow: auto;
    }

    // Tab禁用状态样式
    ::v-deep(.tabs-disabled) {
      .van-tab {
        pointer-events: none;
        opacity: 0.6;
        cursor: not-allowed;
      }
      .van-tabs__nav {
        pointer-events: none;
      }
    }
  }

  // 白色遮罩层样式
  ::v-deep(.transparent-overlay) {
    --van-overlay-background: rgba(255, 255, 255, 0.8);
  }

  // Vant Loading组件的包装样式
  .loading-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .micro-app-container {
    // width: 100%;
    // height: 100%;
    margin-top: 30px;
  }
</style>
