<template>
  <div class="box_container">
    <div class="h5_head">
      <div class="flex">
        <Icon size="30px" name="arrow-left" @click="goBack" />
        <span>{{ t('edit_account') }}</span>
      </div>
    </div>

    <div style="margin-top: 20px">
      <div class="item flex">
        <div class="item_title">{{ t('name') }}：</div>
        <div class="item_content">{{ accountInfo.username }}</div>
      </div>
      <div class="item flex">
        <div class="item_title">{{ t('email') }}：</div>
        <div class="item_content">{{ accountInfo.email }}</div>
      </div>
      <div class="item flex" v-if="accountInfo.oldStatus != 5">
        <div class="item_title">{{ t('status') }}：</div>
        <div class="item_content flex">
          <Switch v-model="accountInfo.status" @change="handleStatusChange" />
          <div class="status_text" style="margin-left: 10px">{{ accountInfo.status ? t('enableText') : t('disableText') }}</div>
        </div>
      </div>
    </div>

    <div class="create_account_field">
      <div class="create_account_field_item">
        <div class="label">{{ t('please_select_network_text') }}</div>
        <div class="plase_select" @click="openNetpointSelect()" v-if="accountInfo.oldStatus != 5">
          <span>{{ t('chooseText') }}</span>
          <Icon name="arrow" />
        </div>
      </div>
      <div v-if="selectEndNetpointData1.length > 0 || selectEndNetpointData2.length > 0" class="flex" style="margin-top: 10px; flex-wrap: wrap">
        <div
          class="create_account_field_item"
          style="margin-bottom: 10px; margin-right: 10px"
          v-for="(item, index) in selectEndNetpointData1"
          :key="index"
        >
          <Tag :plain="false" :round="true" size="large" color="#1990ff">{{ item.label }}</Tag>
        </div>
        <div
          class="create_account_field_item"
          style="margin-bottom: 10px; margin-right: 10px"
          v-for="(item, index) in selectEndNetpointData2"
          :key="index"
        >
          <Tag :plain="false" :round="true" size="large" color="#1990ff">{{ item.label }}</Tag>
        </div>
      </div>
    </div>
    <div class="create_account_field">
      <div class="create_account_field_item">
        <div class="label">{{ t('selectRole') }}</div>
        <div class="plase_select" @click="openRoleSelect()" v-if="accountInfo.oldStatus != 5">
          <span>{{ t('chooseText') }}</span>
          <Icon name="arrow" />
        </div>
      </div>
      <div v-if="selectEndRoleData.length > 0" class="flex" style="flex-wrap: wrap; margin-top: 10px">
        <div
          class="create_account_field_item"
          style="margin-bottom: 10px; margin-right: 10px"
          v-for="(item, index) in selectEndRoleData"
          :key="index"
        >
          <Tag :plain="false" :round="true" size="large" color="#1990ff">{{ item.label }}</Tag>
        </div>
      </div>
    </div>

    <Button style="margin-top: 56px" type="primary" v-if="accountInfo.oldStatus != 5" @click="handleCreateAccount()">{{ t('okText') }}</Button>
  </div>

  <!-- 选择网点 -->
  <Popup
    :show="showNetpoint"
    closeable
    position="bottom"
    :style="{ height: '68%' }"
    @click-overlay="showNetpoint = false"
    @close="showNetpoint = false"
  >
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div>
          <text>{{ t('please_select_network_text') }}</text>
          <text>({{ t('multi_select') }})</text>
        </div>
        <div style="color: #1990ff" @click="handleWangdianConfirm">{{ t('okText') }}</div>
      </div>
      <div class="popup_select_content" style="display: flex; flex-direction: column; gap: 10px">
        <div class="flex" style="flex-wrap: wrap" v-if="selectNetpointData1.length > 0 && accountCategory == '2'">
          <span style="margin-right: 10px">{{ t('dealerCustomerText') }}</span>
          <div class="popup_select_content_item" v-for="(item, index) in selectNetpointData1" :key="index">
            <Tag :plain="!item.isSelect" :round="true" size="large" color="#1990ff" @click="item.isSelect = !item.isSelect">{{ item.label }}</Tag>
          </div>
        </div>
        <div class="flex" style="flex-wrap: wrap" v-if="selectNetpointData2.length > 0 && accountCategory == '3'">
          <span style="margin-right: 10px">{{ t('network_text') }}</span>
          <div class="popup_select_content_item" v-for="(item, index) in selectNetpointData2" :key="index">
            <Tag :plain="!item.isSelect" :round="true" size="large" color="#1990ff" @click="item.isSelect = !item.isSelect">{{ item.label }}</Tag>
          </div>
        </div>
      </div>
    </div>
  </Popup>

  <!-- 选择角色 -->
  <Popup :show="showRole" closeable position="bottom" :style="{ height: '68%' }" @click-overlay="showRole = false" @close="showRole = false">
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popop_select_head_left">
          <text>{{ t('selectRole') }}</text>
          <text>({{ t('multi_select') }})</text>
        </div>
        <div style="color: #1990ff" @click="handleRoleConfirm">{{ t('okText') }}</div>
      </div>
      <div class="popup_select_content" style="display: flex; flex-direction: column; gap: 10px">
        <div class="flex" style="flex-wrap: wrap" v-if="selectRoleData.length > 0">
          <span></span>
          <div class="popup_select_content_item" v-for="(item, index) in selectRoleData" :key="index">
            <Tag :plain="!item.isSelect" :round="true" size="large" color="#1990ff" @click="item.isSelect = !item.isSelect">{{ item.label }}</Tag>
          </div>
        </div>
      </div>
    </div>
  </Popup>
</template>
<script lang="ts" setup name="h5EditAccount">
  import { ref, onMounted, computed } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useUserStore } from '/@/store/modules/user';
  import { showToast, Popup, Switch, Icon, Button, Tag } from 'vant';
  import { querListByUserNetworkApi, getStoreRoleListApi } from '/@/api/h5Api/loginXiangguan';
  import { queryStoreUserInfoApi, updateStoreUserStatusApi, updateStoreUserInfoApi } from '/@/api/h5Api/workTai';

  const { t } = useI18n('common');

  const router = useRouter();
  const route = useRoute();

  const userStore = useUserStore();

  /**
   * 账户类别 1厂端、2总代与子公司、3网点
   * */
  const accountCategory = computed(() => {
    // @ts-ignore
    return userStore.getUserInfo?.accountCategory;
  });

  // 账户信息
  const accountInfo = ref({} as any);

  // 选择网点弹窗是否显示
  const showNetpoint = ref(false);
  // 打开选择网点弹窗
  const openNetpointSelect = () => {
    showNetpoint.value = true;
    selectNetpointData1.value.forEach((forItems) => {
      if (selectEndNetpointData1.value.map((mapItems) => mapItems.value).includes(forItems.value)) {
        forItems.isSelect = true;
      } else {
        forItems.isSelect = false;
      }
    });
    selectNetpointData2.value.forEach((forItems) => {
      if (selectEndNetpointData2.value.map((mapItems) => mapItems.value).includes(forItems.value)) {
        forItems.isSelect = true;
      } else {
        forItems.isSelect = false;
      }
    });
  };
  // 选中的网点数据
  const selectEndNetpointData1 = ref([] as any[]);
  const selectEndNetpointData2 = ref([] as any[]);
  // 选择网点弹窗数据
  const selectNetpointData1 = ref([] as any[]);
  const selectNetpointData2 = ref([] as any[]);
  // 选择网点弹窗确定
  const handleWangdianConfirm = () => {
    const selectData = [...selectNetpointData1.value, ...selectNetpointData2.value].filter((filterItems) => filterItems.isSelect);
    if (selectData.length <= 0) {
      showToast(t('please_select_network_text'));
      return;
    }
    selectEndNetpointData1.value = selectNetpointData1.value.filter((filterItems) => filterItems.isSelect);
    selectEndNetpointData2.value = selectNetpointData2.value.filter((filterItems) => filterItems.isSelect);
    showNetpoint.value = false;
  };

  // 选择角色弹窗是否显示
  const showRole = ref(false);
  // 打开选择角色弹窗
  const openRoleSelect = () => {
    showRole.value = true;
    selectRoleData.value.forEach((forItems) => {
      if (selectEndRoleData.value.map((mapItems) => Number(mapItems.value)).includes(forItems.value)) {
        forItems.isSelect = true;
      } else {
        forItems.isSelect = false;
      }
    });
  };
  // 选中的角色数据
  const selectEndRoleData = ref([] as any[]);
  // 选择角色弹窗数据
  const selectRoleData = ref([] as any[]);
  // 选择角色弹窗确定
  const handleRoleConfirm = () => {
    const selectData = selectRoleData.value.filter((filterItems) => filterItems.isSelect);
    if (selectData.length <= 0) {
      showToast(t('pleaseSelectSameRole'));
      return;
    }
    selectEndRoleData.value = selectData;
    showRole.value = false;
  };

  // 修改状态
  const handleStatusChange = async (value: boolean) => {
    try {
      await updateStoreUserStatusApi({
        userId: accountInfo.value.userId,
        status: value ? 1 : 2,
      });
      showToast(t('operation_successfull'));
    } catch (err) {
      accountInfo.value.status = !accountInfo.value.status;
    }
  };

  // 确定
  const handleCreateAccount = async () => {
    try {
      await updateStoreUserInfoApi({
        userId: accountInfo.value.userId,
        customerCodeList: selectEndNetpointData1.value.map((mapItems) => mapItems.value),
        networkCodeList: selectEndNetpointData2.value.map((mapItems) => mapItems.value),
        roleIdList: selectEndRoleData.value.map((mapItems) => mapItems.value),
      });
      showToast(t('operation_successfull'));
    } catch (err) {
      accountInfo.value.status = !accountInfo.value.status;
    }
  };

  const goBack = () => {
    router.back();
  };

  onMounted(() => {
    // 获取编辑的账户信息
    queryStoreUserInfoApi({ userId: route.query.userId }).then((res) => {
      accountInfo.value = res;
      accountInfo.value = {
        ...res,
        oldStatus: res.status,
        status: res.status == '1' ? true : false,
      };

      // 赋值网点数据
      if (accountCategory.value == '2') {
        const customerNameArr = accountInfo.value.customerName?.split(',') || [];
        selectEndNetpointData1.value = (accountInfo.value.customerCode?.split(',') || [])
          .map((mapItems, mapIndex) => {
            return {
              label: customerNameArr[mapIndex],
              value: mapItems,
              isSelect: true,
            };
          })
          .filter((filterItems) => filterItems.value);
      }

      if (accountCategory.value == '3') {
        const networkNameArr = accountInfo.value.networkName?.split(',') || [];
        selectEndNetpointData2.value = (accountInfo.value.networkCode?.split(',') || [])
          .map((mapItems, mapIndex) => {
            return {
              label: networkNameArr[mapIndex],
              value: mapItems,
              isSelect: true,
            };
          })
          .filter((filterItems) => filterItems.value);
      }
      // 赋值角色数据
      const roleName = accountInfo.value.roleName?.split(',') || [];
      selectEndRoleData.value = (accountInfo.value.role?.split(',') || []).map((mapItems, mapIndex) => {
        return {
          label: roleName[mapIndex],
          value: mapItems,
          isSelect: true,
        };
      });
    });

    querListByUserNetworkApi({ userId: userStore.getUserInfo.userId }).then((res) => {
      const type1Arr = res?.filter((item) => item.type === 2) || [];
      const type2Arr = res?.filter((item) => item.type === 3) || [];
      selectNetpointData1.value = type1Arr.map((mapItems) => {
        return {
          label: mapItems.networkName,
          value: mapItems.networkCode,
          isSelect: false,
        };
      });
      selectNetpointData2.value = type2Arr.map((mapItems) => {
        return {
          label: mapItems.networkName,
          value: mapItems.networkCode,
          isSelect: false,
        };
      });
    });
    getStoreRoleListApi().then((res) => {
      for (let i in res) {
        (res[i] || []).forEach((forItems) => {
          selectRoleData.value.push({
            label: forItems.roleName,
            value: forItems.roleId,
            isSelect: false,
          });
        });
      }
      // 执行对selectRoleData.value每条对象中的roleId相同的进行去重
      const uniqueRoleMap = new Map();
      selectRoleData.value = selectRoleData.value.filter((item) => {
        if (!uniqueRoleMap.has(item.value)) {
          uniqueRoleMap.set(item.value, true);
          return true;
        }
        return false;
      });
    });
  });
</script>
<style scoped lang="less">
  .h5_head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      margin-left: 10px;
      font-size: 20px;
      font-weight: bolder;
    }
    .h5_head_right {
      display: flex;
      align-items: center;
    }
  }

  .item {
    margin-top: 10px;
    .item_title {
      font-size: 16px;
      color: #000000;
    }

    .item_content {
      font-size: 16px;
      color: #999999;
    }
  }

  .popup_select_box {
    width: 100%;
    height: 100%;
    .popup_select_head {
      width: 100%;
      height: 40px;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 50px 10px 16px;
      box-sizing: border-box;
      color: #000000;
      font-weight: bolder;
      .popop_select_head_left {
        line-height: 20px;
      }
    }
    .popup_select_content {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: calc(100% - 40px);
      overflow: auto;
      padding: 16px;
      box-sizing: border-box;
      .popup_select_content_item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  ::v-deep(.van-popup__close-icon--top-right) {
    top: 10px;
  }
  ::v-deep(.van-button) {
    width: 100%;
  }

  .create_account_field {
    margin-top: 28px;
    .create_account_field_item {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .label {
      color: #000000;
      &::before {
        display: inline-block;
        content: '*';
        color: #ff0000;
      }
    }
    .plase_select {
      color: #666666;
    }
  }
</style>
