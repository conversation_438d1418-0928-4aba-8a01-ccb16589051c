<template>
  <div class="common-page box_container">
    <div class="h5_head">
      <div class="flex">
        <Icon size="30px" name="arrow-left" @click="goBack" />
        <span class="title">{{ t('invitation_link_management') }}</span>
      </div>
      <div class="h5_head_right flex" @click="openNetpoint">
        <div class="h5_head_right_item">
          <span class="h5_head_right_item_title">{{ queryParams.networkName ? queryParams.networkName : t('changStore') }}</span>
          <Icon name="exchange" />
        </div>
      </div>
    </div>

    <div class="yanqing_manage flex" style="margin-top: 36px">
      <div class="yanqing_manage_title">
        <div>{{ t('validStatus') }}</div>
      </div>
      <div class="flex" style="margin-left: 10px">
        <div style="margin-right: 10px" v-for="item in validStatus" :key="item.value">
          <Tag :plain="!item.isSelect" :round="true" size="large" color="#1990ff" @click="handleValidStatus(item)">{{ item.label }}</Tag>
        </div>
      </div>
    </div>
    <div class="yanqing_manage flex" style="margin-top: 20px">
      <div class="yanqing_manage_title">
        <div>{{ t('activatedStatus') }}</div>
      </div>
      <div class="flex" style="margin-left: 10px">
        <div style="margin-right: 10px" v-for="item in activeStatus" :key="item.value">
          <Tag :plain="!item.isSelect" :round="true" size="large" color="#1990ff" @click="handleActiveStatus(item)">{{ item.label }}</Tag>
        </div>
      </div>
    </div>
    <div class="yanqing_manage flex" style="margin-top: 20px; flex-wrap: wrap; align-items: flex-start">
      <div class="yanqing_manage_title">
        <div>{{ t('role_filter') }}</div>
      </div>
      <div class="flex" style="margin-left: 10px; flex-wrap: wrap; flex: 1; overflow: hidden" :style="{ height: isSqOrZk ? '26px' : 'auto' }">
        <div style="margin-right: 10px; margin-bottom: 10px" v-for="item in roleFilter" :key="item.value">
          <Tag :plain="!item.isSelect" :round="true" size="large" color="#1990ff" @click="handleRoleFilter(item)">{{ item.label }}</Tag>
        </div>
      </div>
      <div style="width: 100%; text-align: center; color: #1990ff" @click="isSqOrZk = !isSqOrZk">
        <span>{{ isSqOrZk ? t('form_unfold') : t('form_putAway') }}</span>
        <Icon name="arrow-down" v-if="isSqOrZk" />
        <Icon name="arrow-up" v-else />
      </div>
    </div>

    <div style="margin-top: 20px">
      <Empty :text="t('noInvitationLinkManagement')" v-if="invitationLinkList.length === 0" />
      <div v-else class="invitation_link_list">
        <div class="invitation_link_list_item" v-for="item in invitationLinkList" :key="item.id" @click="handleInvitationLink(item)">
          <div class="item">
            <div class="item_title">{{ t('network_text') }}：</div>
            <div class="item_content" v-if="accountCategory == '2'">{{ item.customerName ? item.customerName : '' }}</div>
            <div class="item_content" v-else-if="accountCategory == '3'">{{ item.networkName ? item.networkName : '' }}</div>
          </div>
          <div class="item">
            <div class="item_title">{{ t('role_text') }}：</div>
            <div class="item_content">{{ item.roleName }}</div>
          </div>
          <div class="item">
            <div class="item_title">{{ t('status') }}：</div>
            <div class="item_content" :style="{ color: item.statusColor }">{{ item.effectiveStatusName }}</div>
          </div>
          <div class="item">
            <div class="item_title">{{ t('createTime') }}：</div>
            <div class="item_content">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="look_detail">
            <Icon name="arrow" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 门店选择 -->
  <Popup :show="showNetpoint" :closeable="false" position="bottom" :style="{ height: '38%' }" @click-overlay="showNetpoint = false">
    <Picker :columns="columns" @cancel="showNetpoint = false" @confirm="confirmSubmit" />
  </Popup>

  <!-- 邀请链接查看 -->
  <Popup
    :show="showCreateInviteLinkSuccess"
    @close="showCreateInviteLinkSuccess = false"
    :closeable="true"
    position="center"
    :style="{ width: '86%', minHeight: '44%' }"
  >
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popup_select_head_left">
          <text>{{ t('inviteLink') }}{{ createInviteLinkSuccessInfo.effectiveStatusName }}</text>
        </div>
      </div>
      <div style="display: flex; flex-direction: column; padding: 0 16px; margin-top: 20px">
        <div>
          <span>{{ t('loginAddress') }}：</span>
          <span>{{ createInviteLinkSuccessInfo.loginUrl }}</span>
        </div>
        <div>
          <span>{{ t('authAccount') }}：</span>
          <span>{{ createInviteLinkSuccessInfo.account }}</span>
        </div>
        <div>
          <span>{{ t('authPassword') }}：</span>
          <span>{{ createInviteLinkSuccessInfo.password }}</span>
        </div>
        <div>{{ t('thisAuthInfoIsEffectiveFor7Days') }}：{{ createInviteLinkSuccessInfo.endTime }}</div>
      </div>
      <div
        style="padding: 0 16px; margin-top: 20px; padding-bottom: 16px"
        v-if="createInviteLinkSuccessInfo.activationStatus == '1' && createInviteLinkSuccessInfo.effectiveStatus == '1'"
      >
        <Button type="primary" @click="handleCopyInviteLink()">{{ t('copyInviteLink') }}</Button>
        <div style="margin-top: 16px; color: #666666">{{ t('sendLinkToCorrespondingPositions') }}</div>
      </div>
    </div>
  </Popup>
</template>
<script lang="ts" setup name="h5InvitationLinkManagement">
  import { ref, onMounted, computed, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import dayjs from 'dayjs';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useUserStore } from '/@/store/modules/user';
  import { showToast, Popup, Picker, Tag, Icon, Button } from 'vant';
  import 'vant/lib/index.css';
  import Empty from '/@/views/h5/components/empty.vue';
  import { querListByUserNetworkApi, getStoreRoleListApi } from '/@/api/h5Api/loginXiangguan';
  import { queryUserLinkPageApi } from '/@/api/h5Api/workTai';

  const { t } = useI18n('common');

  const router = useRouter();
  const userStore = useUserStore();

  // 是否收起展开角色
  const isSqOrZk = ref(true);

  /**
   * 账户类别 1厂端、2总代与子公司、3网点
   * */
  const accountCategory = computed(() => {
    // @ts-ignore
    return userStore.getUserInfo?.accountCategory;
  });

  // 选择门店（网点）弹窗
  const showNetpoint = ref(false);
  const columns = ref([] as any[]);
  const confirmSubmit = (params: any) => {
    // 全部选择中
    if (params.selectedOptions[0].value === '') {
      queryParams.value.networkList = [];
      queryParams.value.customerList = [];
      // 全部时候需要将columns.value字段赋值给params，type=2时赋值networkList，type=3时赋值customerList
      columns.value.forEach((item: any) => {
        if (item.type === 3) {
          queryParams.value.networkList.push(item.value);
        } else if (item.type === 2) {
          queryParams.value.customerList.push(item.value);
        }
      });
      queryParams.value.networkName = t('all');
      showNetpoint.value = false;
      initData();
      return;
    }
    if (params.selectedOptions[0].type === 2) {
      queryParams.value.networkList = [];
      queryParams.value.customerList = [params.selectedOptions[0].value];
    } else if (params.selectedOptions[0].type === 3) {
      queryParams.value.customerList = [];
      queryParams.value.networkList = [params.selectedOptions[0].value];
    }
    queryParams.value.networkName = params.selectedOptions[0].text;
    showNetpoint.value = false;
    initData();
  };

  // 邀请链接查看
  const showCreateInviteLinkSuccess = ref(false);
  const createInviteLinkSuccessInfo = ref({
    loginUrl: `${window.location.origin}/mobile/h5Login`,
    account: '',
    password: '',
    endTime: '',
    effectiveStatus: '', // 有效状态
    activationStatus: '', // 激活状态
    effectiveStatusName: '',
  });
  // 复制邀请链接
  const handleCopyInviteLink = () => {
    const info = createInviteLinkSuccessInfo.value;
    const text = `${t('loginAddress')}：\n${info.loginUrl}\n\n${t('authAccount')}：\n${info.account}\n\n${t('authPassword')}：\n${info.password}\n\n${t('thisAuthInfoIsEffectiveFor7Days')}：${info.endTime}`;
    navigator.clipboard.writeText(text);
    showToast(t('copySuccess'));
  };

  // 邀请链接列表
  const invitationLinkList = ref([] as any[]);

  // 查询邀请链接列表参数
  const queryParams = ref({
    pageIndex: 1, // 页码
    pageSize: 1000, // 每页条数
    regionCode: '', // 区域
    countryCode: '', // 国家
    customerList: [] as any[], // B端客户
    networkList: [] as any[], // 网点
    networkName: '', // 网点名称
    roleId: '', // 角色id
    effectiveStatus: '', // 有效状态
    activationStatus: '', // 激活状态
  });

  // 有效状态
  const validStatus = ref([
    {
      label: t('valid'),
      value: 1,
      isSelect: false,
    },
    {
      label: t('invalid'),
      value: 2,
      isSelect: false,
    },
    {
      label: t('expired'),
      value: 3,
      isSelect: false,
    },
  ]);
  const handleValidStatus = (item: any) => {
    validStatus.value.forEach((items) => {
      if (items.value === item.value) {
        items.isSelect = !items.isSelect;
        queryParams.value.effectiveStatus = items.isSelect ? item.value : '';
      } else {
        items.isSelect = false;
      }
    });
    initData();
  };

  // 激活状态
  const activeStatus = ref([
    {
      label: t('activated'),
      value: 2,
      isSelect: false,
    },
    {
      label: t('unactivated'),
      value: 1,
      isSelect: false,
    },
  ]);
  const handleActiveStatus = (item: any) => {
    activeStatus.value.forEach((items) => {
      if (items.value === item.value) {
        items.isSelect = !items.isSelect;
        queryParams.value.activationStatus = items.isSelect ? item.value : '';
      } else {
        items.isSelect = false;
      }
    });
    initData();
  };

  // 角色筛选
  const roleFilter = ref([] as any[]);
  const handleRoleFilter = (item: any) => {
    roleFilter.value.forEach((items) => {
      if (items.value === item.value) {
        items.isSelect = !items.isSelect;
        queryParams.value.roleId = items.isSelect ? item.value : '';
      } else {
        items.isSelect = false;
      }
    });
    initData();
  };

  const goBack = () => {
    const currentRoute = unref(router.currentRoute);
    const backUrl = (currentRoute.query.backUrl || '') as string;
    if (backUrl) {
      router.push({
        path: backUrl,
      });
      return;
    }
    router.back();
  };

  // 初始化数据
  const initData = async () => {
    try {
      const res = await queryUserLinkPageApi(queryParams.value);
      invitationLinkList.value = ((res || []) as any[]).map((mapItems) => {
        let effectiveStatusName = '';
        let statusColor = '#1989fa';
        switch (mapItems.activationStatus) {
          case 2:
            effectiveStatusName = effectiveStatusName + t('activated');
            break;
          case 1:
            effectiveStatusName = effectiveStatusName + t('unactivated');
            statusColor = '#f12727';
            break;
        }
        switch (mapItems.effectiveStatus) {
          case 1:
            effectiveStatusName = effectiveStatusName + t('valid');
            break;
          case 2:
            effectiveStatusName = effectiveStatusName + t('invalid');
            statusColor = '#f12727';
            break;
          case 3:
            effectiveStatusName = effectiveStatusName + t('expired');
            statusColor = '#f12727';
            break;
        }
        return {
          ...mapItems,
          effectiveStatusName,
          statusColor,
        };
      });
    } catch (err) {
      console.log(err);
      invitationLinkList.value = [];
    }
  };

  // 邀请链接查看
  const handleInvitationLink = (item: any) => {
    let effectiveStatusName = '';
    switch (item.activationStatus) {
      case 2:
        effectiveStatusName = effectiveStatusName + t('activated');
        break;
      case 1:
        effectiveStatusName = effectiveStatusName + t('unactivated');
        break;
    }
    switch (item.effectiveStatus) {
      case 1:
        effectiveStatusName = effectiveStatusName + t('valid');
        break;
      case 2:
        effectiveStatusName = effectiveStatusName + t('invalid');
        break;
      case 3:
        effectiveStatusName = effectiveStatusName + t('expired');
        break;
    }
    createInviteLinkSuccessInfo.value = {
      ...createInviteLinkSuccessInfo.value,
      account: item.userCode,
      password: item.authPwd,
      endTime: dayjs(item.createTime).add(7, 'day').format('YYYY-MM-DD HH:mm'),
      effectiveStatus: item.effectiveStatus,
      activationStatus: item.activationStatus,
      effectiveStatusName,
    };
    showCreateInviteLinkSuccess.value = true;
  };

  const openNetpoint = () => {
    if (!columns.value.length) {
      // 可以加个 loading 或提示
      return;
    }
    showNetpoint.value = true;
  };

  onMounted(() => {
    querListByUserNetworkApi({ userId: userStore.getUserInfo.userId }).then((res) => {
      // 增加如果res只有一条数据或者不存在数据无需添加全部
      if (res.length > 1) {
        columns.value = [
          {
            text: t('all'),
            value: '',
          },
        ];
      }
      columns.value = [
        ...columns.value,
        ...res
          ?.filter((filterItems) => {
            return accountCategory.value == filterItems.type;
          })
          .map((mapItems) => {
            return {
              text: mapItems.networkName,
              value: mapItems.networkCode,
              type: mapItems.type,
            };
          }),
      ];
      /**
       * 如果columns.value的长度大于1条:networkName=t('all')
       * 如果=一条，取第一条的label设置给networkName
       * */
      if (columns.value.length > 1) {
        queryParams.value.networkName = t('all');
      } else if (columns.value.length == 1) {
        queryParams.value.networkName = columns.value[0].text;
      }
      // 全部时候需要将columns.value字段赋值给params，type=2时赋值networkList，type=3时赋值customerList
      columns.value.forEach((item: any) => {
        if (item.type === 3) {
          queryParams.value.networkList.push(item.value);
        } else if (item.type === 2) {
          queryParams.value.customerList.push(item.value);
        }
      });
      initData();
    });
    getStoreRoleListApi().then((res) => {
      for (let i in res) {
        (res[i] || []).forEach((forItems) => {
          roleFilter.value.push({
            label: forItems.roleName,
            value: forItems.roleId,
            isSelect: false,
          });
        });
      }
      // 执行对roleFilter.value每条对象中的roleId相同的进行去重
      const uniqueRoleMap = new Map();
      roleFilter.value = roleFilter.value.filter((item) => {
        if (!uniqueRoleMap.has(item.value)) {
          uniqueRoleMap.set(item.value, true);
          return true;
        }
        return false;
      });
    });
  });
</script>
<style scoped lang="less">
  .common-page {
    width: 100%;
    min-height: 100vh;
    padding: 20px 0;
    box-sizing: border-box;
    background: #ffffff;
  }
  .h5_head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      margin-left: 10px;
      font-size: 20px;
      font-weight: bolder;
    }
    .h5_head_right {
      display: flex;
      align-items: center;
      .h5_head_right_item {
        font-size: 14px;
        display: flex;
        align-items: center;
        .h5_head_right_item_title {
          width: 100px;
          text-align: right;
          margin-right: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .yanqing_manage {
    align-items: center;
    .yanqing_manage_title {
      font-size: 14px;
      font-weight: bolder;
    }
  }

  .invitation_link_list_item {
    position: relative;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    padding: 10px 30px 10px 10px;
    margin-bottom: 10px;
    .item {
      display: flex;
      justify-content: space-between;
    }
    .item_content {
      color: #999;
      flex: 1;
    }
    .look_detail {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }

  .popup_select_box {
    width: 100%;
    height: 100%;
    .popup_select_head {
      width: 100%;
      height: 40px;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 50px 10px 16px;
      box-sizing: border-box;
      color: #000000;
      font-weight: bolder;
      .popup_select_head_left {
        line-height: 20px;
      }
    }
    .popup_select_content {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: calc(100% - 40px);
      overflow: auto;
      padding: 16px;
      box-sizing: border-box;
      .popup_select_content_item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  ::v-deep(.van-popup__close-icon--top-right) {
    top: 10px;
  }
  ::v-deep(.van-button) {
    width: 100%;
  }
</style>
