<template>
  <div class="box_container">
    <div class="h5_head">
      <img :src="logoImg" />
    </div>

    <div class="go_back" @click="goBack()">
      <Icon name="arrow-left" />
      <span>{{ t('static_state_route_forget_password') }}</span>
    </div>

    <!-- 忘记密码 -->
    <div class="login_box">
      <div class="input_box">
        <div class="input_item">
          <div class="input_item_label">
            <span>{{ t('email') }}</span>
          </div>
          <div class="input_item_input">
            <input type="text" v-model="formData.account" autocomplete="off" />
          </div>
        </div>
        <div class="input_item">
          <div class="input_item_label">
            <span>{{ t('modify_login_password') }}</span>
          </div>
          <div class="input_item_input">
            <input type="password" v-model="formData.password" autocomplete="off" />
          </div>
        </div>
        <div class="input_item">
          <div class="input_item_label">
            <span>{{ t('confirm_password') }}</span>
          </div>
          <div class="input_item_input">
            <input type="password" v-model="formData.password2" autocomplete="off" />
          </div>
        </div>
        <div class="input_item">
          <div class="input_item_label">
            <span>{{ t('inputCode') }}</span>
          </div>
          <div class="input_item_input send_sms flex">
            <input type="text" v-model="formData.code" autocomplete="off" />
            <Button type="primary" v-if="!isShowDjs" @click.stop="sendSms()">{{ t('send_verification_code') }}</Button>
            <count-down :time="djsTime" v-else @finish="isShowDjs = false">
              <template #default="timeData">
                <div class="djs_item">{{ timeData.seconds }}</div>
              </template>
            </count-down>
          </div>
        </div>
      </div>
      <Button type="primary" @click="handleSubmit()">{{ t('okText') }}</Button>
    </div>
  </div>
</template>
<script lang="ts" setup name="h5Login">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { showToast, CountDown, Button, Icon } from 'vant';
  import 'vant/lib/index.css';
  import 'vant/lib/button/index.css';
  import { defHttp } from '/@/utils/http/axios';
  import logoImg from '/@/assets/images/logo.png';
  import { EmailRegex } from '/@/utils/helper/validator';
  import { passwordRule } from '/@/utils/validator';
  import { forgetSendSmsApi } from '/@/api/common/api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import rsaEncryptor from '/@/utils/rsaEncrypt';

  const { t } = useI18n('common');
  const router = useRouter();

  // 倒计时
  const djsTime = ref(60 * 1000);
  // 是否显示倒计时
  const isShowDjs = ref(false);

  // 修改密码表单
  const formData = ref({
    account: '',
    code: '',
    userName: '',
    // 密码-激活账号页面传入
    password: '',
    // 再次输入密码
    password2: '',
  });

  const loading = ref(false);

  // 发送验证码
  const sendSms = async () => {
    if (!EmailRegex.test(formData.value.account)) {
      showToast({ message: t('enterFormatEmail') });
      return;
    }
    if (loading.value) return;
    loading.value = true;
    try {
      await forgetSendSmsApi({
        account: formData.value.account,
      });
      isShowDjs.value = true;
      loading.value = false;
    } catch (err) {
      loading.value = false;
    }
  };

  // 提交
  const handleSubmit = async () => {
    // 邮箱不能为空
    if (!formData.value.account) {
      showToast({ message: t('pleaseEnterEmail') });
      return;
    }
    if (!EmailRegex.test(formData.value.account)) {
      showToast({ message: t('enterFormatEmail') });
      return;
    }
    // 密码不能为空
    if (!formData.value.password) {
      showToast({ message: t('passwordPlaceholder') });
      return;
    }
    // 两次密码不一致
    if (formData.value.password !== formData.value.password2) {
      showToast({ message: t('twoPasswordsDoNotMatch') });
      return;
    }
    // 密码格式不正确
    if (!passwordRule.test(formData.value.password2)) {
      showToast({ message: `${t('password_rule1')};${t('password_rule2')}` });
      return;
    }
    // 验证码不能为空
    if (!formData.value.code) {
      showToast({ message: t('inputCodePlaceholder') });
      return;
    }
    if (loading.value) return;
    loading.value = true;
    // 提交
    defHttp
      .post(
        {
          url: '/forgotPwd',
          params: {
            ...formData.value,
            password: rsaEncryptor.encrypt(formData.value.password),
            password2: rsaEncryptor.encrypt(formData.value.password2),
          },
        },
        { isTransformResponse: false }
      )
      .then((res) => {
        if (res.code === 0) {
          showToast({ message: t('password_change_success') });
          loading.value = false;
          goBack();
        } else {
          showToast({ message: res.msg });
          loading.value = false;
        }
      });
  };

  const goBack = () => {
    router.back();
  };
</script>
<style scoped lang="less">
  .h5_head {
    width: 60px;
  }

  ::v-deep(.van-button) {
    width: 100%;
  }

  .go_back {
    width: 100%;
    display: flex;
    align-items: center;
    margin-top: 50px;
    font-size: 16px;
    font-family: bolrder;
    > span {
      margin-left: 10px;
    }
  }

  .input_box {
    margin-top: 30px;
    .input_item {
      margin-bottom: 20px;
      .input_item_label {
        font-size: 14px;
        color: #333;
        margin-bottom: 10px;
      }
      .input_item_input {
        input {
          width: 100%;
          height: 40px;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          padding: 0 10px;
        }
      }
    }
  }

  .send_sms ::v-deep(.van-button) {
    width: 186px;
    margin-left: 10px;
  }

  .djs_item {
    width: 56px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    color: #fff;
    font-size: 12px;
    text-align: center;
    background-color: #1989fa;
    margin-left: 10px;
    border-radius: 4px;
  }
</style>
