<template>
  <div class="box_container">
    <div class="h5_login">
      <img :src="logoImg" />
      <div class="h5_head_right_item">
        <AppLocalePicker :reload="true" class="enter-x xl:text-gray-600" :showText="true" />
      </div>
    </div>
    <div></div>

    <div class="h5_login_tabs">
      <Tabs type="card" v-model="tabActive" @change="changeTab">
        <Tab name="login" :title="t('loginButton')" />
        <Tab name="account_activation" :title="t('account_activation')" />
      </Tabs>
    </div>
    <!-- 登录 -->
    <div class="login_box" v-if="tabActive === 'login'">
      <div class="input_box">
        <div class="input_item">
          <div class="input_item_label">
            <span>{{ t('email') }}</span>
          </div>
          <div class="input_item_input">
            <input v-model="formData.emali" type="text" autocomplete="off" />
          </div>
        </div>
        <div class="input_item">
          <div class="input_item_label">
            <span>{{ t('login_password') }}</span>
          </div>
          <div class="input_item_input">
            <input v-model="formData.password" type="password" autocomplete="off" />
          </div>
        </div>
      </div>
      <div :class="['forget_password', { fixArClass: getLocale === LOCALE.AR }]" @click="handleForgetPassword()"
        >{{ t('static_state_route_forget_password') }}?</div
      >
      <Button :disabled="!formData.emali || !formData.password" type="primary" @click="handleLogin()">{{ t('login_button') }}</Button>
    </div>

    <!-- 账户激活 -->
    <div class="account_activation" v-else>
      <activationAccount />
    </div>
  </div>
</template>
<script lang="ts" setup name="h5Login">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useUserStore } from '/@/store/modules/user';
  import { showToast, Tab, Tabs, Button } from 'vant';
  import 'vant/lib/index.css';
  import { EmailRegex } from '/@/utils/helper/validator';
  import { AppLocalePicker } from '/@/components/Application';
  import activationAccount from './components/activationAccount/index.vue';
  import logoImg from '/@/assets/images/logo.png';
  import { useLocale } from '/@/locales/useLocale';
  import { LOCALE } from '/@/settings/localeSetting';

  const { getLocale } = useLocale();
  import rsaEncryptor from '/@/utils/rsaEncrypt';

  const { t } = useI18n('common');

  const router = useRouter();

  const userStore = useUserStore();

  // tab标签
  const tabActive = ref('login');
  const changeTab = (index) => {
    tabActive.value = index;
  };

  // 登录loading
  const loginLoading = ref(false);

  // 登录表单
  const formData = ref({
    emali: '',
    password: '',
  });

  // 忘记密码
  const handleForgetPassword = () => {
    router.push('/mobile/h5ForgetPassword');
  };

  /**
   * 账号或者手机登录
   */
  async function handleLogin() {
    if (!formData.value.emali) {
      showToast({ message: t('pleaseEnterEmail') });
      return;
    }
    if (!EmailRegex.test(formData.value.emali)) {
      showToast({ message: t('enterFormatEmail') });
      return;
    }
    if (!formData.value.password) {
      showToast({ message: t('passwordPlaceholder') });
      return;
    }
    if (loginLoading.value) {
      return;
    }
    try {
      loginLoading.value = true;
      await userStore.login({
        account: formData.value.emali,
        password: rsaEncryptor.encrypt(formData.value.password) as string,
        code: '123123',
        isH5: true,
      });
    } catch (error) {
    } finally {
      loginLoading.value = false;
    }
  }
</script>
<style scoped lang="less">
  .h5_login {
    display: flex;
    justify-content: space-between;
    align-items: center;
    img {
      width: 60px;
    }
  }
  .h5_login_tabs {
    margin-top: 36px;
  }

  ::v-deep(.van-tabs__nav--card) {
    margin: 0;
  }
  ::v-deep(.van-button) {
    width: 100%;
  }
  .input_box {
    margin-top: 30px;
    .input_item {
      margin-bottom: 20px;
      .input_item_label {
        font-size: 14px;
        color: #333;
        margin-bottom: 10px;
      }
      .input_item_input {
        input {
          width: 100%;
          height: 40px;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          padding: 0 10px;
        }
      }
    }
  }
  .forget_password {
    font-size: 12px;
    color: #1989fa;
    text-align: right;
    margin-top: 10px;
    cursor: pointer;
    margin-bottom: 10px;
  }
  .fixArClass {
    text-align: left;
  }
</style>
<style>
  .van-button--disabled {
    opacity: 0.2;
  }
</style>
