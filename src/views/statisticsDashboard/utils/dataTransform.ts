/**
 * 数据转换工具
 * 将API返回的数据转换为图表组件所需的格式
 */

import { ClueSourceEnum } from '../enums';
import type { AllClueSourceResponseType } from '../api';
import type { MonthlyChannelData } from '../types/statisticDashboard';

/**
 * 一级来源ID到内部渠道key的映射
 */
export const CLUE_SOURCE_ID_TO_KEY_MAP: Record<string, string> = {
  [ClueSourceEnum.ONE_SOURCE_ONLINE_PUB]: 'onlinePublic',
  [ClueSourceEnum.ONE_SOURCE_ONLINE_PRI]: 'onlinePrivate',
  [ClueSourceEnum.ONE_SOURCE_OFFLINE_PRI]: 'offlinePrivate',
};

/**
 * 二级来源ID到内部子渠道key的映射
 */
export const CLUE_SOURCE_SECOND_ID_TO_KEY_MAP: Record<string, string> = {
  // 线上私域二级来源
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_APP]: 'app',
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_WEB]: 'website',
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_CUSTOM]: 'serviceCenter',

  // 线下私域二级来源
  [ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_MARKETING]: 'marketing',
  [ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_CARSHOW]: 'carshow',

  // 线上公域二级来源
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_SOCIAL]: 'social',
  [ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_VERTICAL]: 'vertical',
};

/**
 * 内部渠道key到一级来源ID的反向映射
 */
export const KEY_TO_CLUE_SOURCE_ID_MAP: Record<string, string> = Object.fromEntries(
  Object.entries(CLUE_SOURCE_ID_TO_KEY_MAP).map(([id, key]) => [key, id])
);

/**
 * 内部子渠道key到二级来源ID的反向映射
 */
export const KEY_TO_CLUE_SOURCE_SECOND_ID_MAP: Record<string, string> = Object.fromEntries(
  Object.entries(CLUE_SOURCE_SECOND_ID_TO_KEY_MAP).map(([id, key]) => [key, id])
);

/**
 * 根据一级来源ID获取其对应的二级来源ID列表
 */
export const getSecondSourceIdsByFirstSourceId = (firstSourceId: string): string[] => {
  const channelKey = CLUE_SOURCE_ID_TO_KEY_MAP[firstSourceId];
  if (!channelKey) return [];

  // 根据一级来源类型返回对应的二级来源
  switch (firstSourceId) {
    case ClueSourceEnum.ONE_SOURCE_ONLINE_PRI:
      return [ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_APP, ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_WEB, ClueSourceEnum.TWO_SOURCE_ONLINE_PRI_CUSTOM];
    case ClueSourceEnum.ONE_SOURCE_OFFLINE_PRI:
      return [ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_MARKETING, ClueSourceEnum.TWO_SOURCE_OFFLINE_PRI_CARSHOW];
    case ClueSourceEnum.ONE_SOURCE_ONLINE_PUB:
      return [ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_SOCIAL, ClueSourceEnum.TWO_SOURCE_ONLINE_PUB_VERTICAL];
    default:
      return [];
  }
};

/**
 * 转换API返回的一级来源数据为图表数据格式
 */
export function transformApiDataToChartData(apiResponse: AllClueSourceResponseType): MonthlyChannelData[] {
  const result: MonthlyChannelData[] = [];

  // 遍历每个统计日期的数据
  apiResponse.clueOneSourceResponseList.forEach((dateItem) => {
    const chartDataItem: MonthlyChannelData = {
      name: dateItem.statisticsDate,
      value: 0,
      type: 'period',
      channels: {},
      subChannels: {}, // 一级数据时二级为空，下探时再填充
    };

    // 转换一级来源数据
    dateItem.clueOneSourceDtoList.forEach((sourceItem) => {
      const channelKey = CLUE_SOURCE_ID_TO_KEY_MAP[sourceItem.oneSourceId];
      if (channelKey) {
        chartDataItem.channels[channelKey] = sourceItem.oneSourceCount;
        chartDataItem.value += sourceItem.oneSourceCount;
      } else {
        console.warn(`未知的一级来源ID: ${sourceItem.oneSourceId}`);
      }
    });

    result.push(chartDataItem);
  });

  return result;
}

/**
 * 转换API返回的二级来源数据为图表数据格式
 */
export function transformSecondApiDataToChartData(apiResponse: AllClueSourceResponseType, parentSourceId: string): MonthlyChannelData[] {
  const result: MonthlyChannelData[] = [];
  const parentChannelKey = CLUE_SOURCE_ID_TO_KEY_MAP[parentSourceId];

  if (!parentChannelKey) {
    console.warn(`未知的父级来源ID: ${parentSourceId}`);
    return result;
  }

  // 遍历每个统计日期的二级数据
  apiResponse.clueTwoSourceResponseList?.forEach((dateItem) => {
    const chartDataItem: MonthlyChannelData = {
      name: dateItem.statisticsDate,
      value: 0,
      type: 'period',
      channels: {},
      subChannels: {
        [parentChannelKey]: {},
      },
    };

    // 转换二级来源数据
    dateItem.clueTwoSourceDtoList.forEach((sourceItem) => {
      const subChannelKey = CLUE_SOURCE_SECOND_ID_TO_KEY_MAP[sourceItem.twoSourceId];
      if (subChannelKey) {
        chartDataItem.subChannels![parentChannelKey][subChannelKey] = sourceItem.twoSourceCount;
        chartDataItem.value += sourceItem.twoSourceCount;
      } else {
        console.warn(`未知的二级来源ID: ${sourceItem.twoSourceId}`);
      }
    });

    result.push(chartDataItem);
  });

  return result;
}

/**
 * 合并一级和二级数据
 */
export function mergeFirstAndSecondLevelData(
  firstLevelData: MonthlyChannelData[],
  secondLevelDataMap: Record<string, MonthlyChannelData[]>
): MonthlyChannelData[] {
  return firstLevelData.map((firstItem) => {
    const mergedItem: MonthlyChannelData = {
      ...firstItem,
      subChannels: {},
    };

    // 为每个一级渠道添加对应的二级数据
    Object.keys(firstItem.channels).forEach((channelKey) => {
      const sourceId = KEY_TO_CLUE_SOURCE_ID_MAP[channelKey];
      const secondData = secondLevelDataMap[sourceId];

      if (secondData) {
        const matchingSecondItem = secondData.find((item) => item.name === firstItem.name);
        if (matchingSecondItem?.subChannels?.[channelKey]) {
          mergedItem.subChannels![channelKey] = matchingSecondItem.subChannels[channelKey];
        }
      }
    });

    return mergedItem;
  });
}

/**
 * 数据验证结果类型
 */
export interface DataValidationResult {
  isValid: boolean;
  isEmpty: boolean;
  error?: string;
}

/**
 * 数据验证函数 - 区分空数据和错误数据
 */
export function validateTransformedData(data: MonthlyChannelData[]): DataValidationResult {
  // 检查数据是否为数组
  if (!Array.isArray(data)) {
    console.warn('转换后的数据格式错误，不是数组');
    return { isValid: false, isEmpty: false, error: '数据格式错误' };
  }

  // 检查是否为空数据
  if (data.length === 0) {
    console.info('转换后的数据为空');
    return { isValid: true, isEmpty: true };
  }

  // 验证数据项格式
  for (const item of data) {
    if (!item.name || typeof item.value !== 'number' || !item.channels) {
      console.warn('数据项格式错误:', item);
      return { isValid: false, isEmpty: false, error: '数据项格式错误' };
    }
  }

  return { isValid: true, isEmpty: false };
}

/**
 * 兼容性函数 - 保持向后兼容
 * @deprecated 请使用新的 validateTransformedData 函数
 */
export function validateTransformedDataLegacy(data: MonthlyChannelData[]): boolean {
  const result = validateTransformedData(data);
  return result.isValid && !result.isEmpty;
}

/**
 * 获取数据统计信息
 */
export function getDataStatistics(data: MonthlyChannelData[]) {
  const totalRecords = data.length;
  const totalValue = data.reduce((sum, item) => sum + item.value, 0);
  const dateRange =
    data.length > 0
      ? {
          start: data[0].name,
          end: data[data.length - 1].name,
        }
      : null;

  const channelStats = data.reduce(
    (stats, item) => {
      Object.entries(item.channels).forEach(([channel, value]) => {
        stats[channel] = (stats[channel] || 0) + value;
      });
      return stats;
    },
    {} as Record<string, number>
  );

  return {
    totalRecords,
    totalValue,
    dateRange,
    channelStats,
  };
}
