import { ref, nextTick } from 'vue';

/**
 * 筛选器吸顶功能Hook
 *
 * 功能特点：
 * - 自动检测滚动位置，智能切换吸顶状态
 * - 动态计算头部偏移量（头部区域 + 导航栏）
 * - 支持自定义触发阈值和偏移量
 * - 提供流畅的切换动画效果
 * - 响应式高度计算，适配不同屏幕尺寸
 */

interface StickyFilterOptions {
  /** 额外的顶部偏移量 */
  extraTopOffset?: number;
  /** 触发吸顶的额外阈值 */
  triggerThreshold?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
  /** 头部区域选择器 */
  headerSelector?: string;
  /** 导航栏选择器 */
  navSelector?: string;
}

interface StickyFilterReturn {
  /** 筛选器容器引用 */
  filterContainerRef: any;
  /** 筛选器内容引用 */
  filterContentRef: any;
  /** 是否处于吸顶状态 */
  isSticky: any;
  /** 筛选器高度 */
  filterHeight: any;
  /** 筛选器原始宽度 */
  originalWidth: any;
  /** 筛选器原始左偏移 */
  originalLeft: any;
  /** 顶部偏移量 */
  topOffset: any;
  /** 当前滚动位置 */
  scrollTop: any;
  /** 触发吸顶的阈值 */
  stickyThreshold: any;
  /** 初始化函数 */
  initStickyFilter: () => Promise<void>;
  /** 销毁函数 */
  destroyStickyFilter: () => void;
  /** 手动更新高度 */
  updateDimensions: () => Promise<void>;
}

export function useStickyFilter(options: StickyFilterOptions = {}): StickyFilterReturn {
  const { extraTopOffset = 10, triggerThreshold = 10, headerSelector = '.ant-layout-header', navSelector = '.ant-tabs-nav' } = options;

  // 响应式状态
  const filterContainerRef = ref<HTMLElement>();
  const filterContentRef = ref<HTMLElement>();
  const isSticky = ref(false);
  const filterHeight = ref(0);
  const originalWidth = ref(0);
  const originalLeft = ref(0);
  const topOffset = ref(0);
  const scrollTop = ref(0);
  const stickyThreshold = ref(0);

  // 头部和导航栏高度
  const headerHeight = ref(0);
  const navHeight = ref(0);

  // 滚动处理函数
  let ticking = false;
  const handleScroll = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        updateScrollState();
        ticking = false;
      });
      ticking = true;
    }
  };

  /**
   * 更新滚动状态
   */
  const updateScrollState = () => {
    const currentScrollTop = document.body.scrollTop;
    scrollTop.value = currentScrollTop;

    const shouldStick = currentScrollTop > stickyThreshold.value;

    if (shouldStick !== isSticky.value) {
      isSticky.value = shouldStick;
    }
  };

  /**
   * 计算头部区域高度
   */
  const calculateHeaderHeights = async (): Promise<void> => {
    await nextTick();

    // 计算头部区域高度
    const headerEl = document.querySelector(headerSelector) as HTMLElement;
    if (headerEl) {
      headerHeight.value = headerEl.offsetHeight;
    }

    // 计算导航栏高度
    const navEl = document.querySelector(navSelector) as HTMLElement;

    if (navEl) {
      navHeight.value = navEl.offsetHeight;
    }

    // 计算总的顶部偏移量
    topOffset.value = headerHeight.value + navHeight.value + extraTopOffset;
  };

  /**
   * 计算筛选器尺寸和阈值
   */
  const calculateFilterDimensions = async (): Promise<void> => {
    await nextTick();

    if (!filterContentRef.value) {
      console.warn('筛选器内容引用未找到');
      return;
    }

    // 获取筛选器高度和宽度
    const rect = filterContentRef.value.getBoundingClientRect();
    filterHeight.value = rect.height;
    originalWidth.value = rect.width;
    originalLeft.value = rect.left;

    // 获取筛选器在页面中的位置
    const containerRect = filterContainerRef.value?.getBoundingClientRect();
    const offsetTop = containerRect ? window.pageYOffset + containerRect.top : 0;

    // 计算触发阈值
    stickyThreshold.value = offsetTop + filterHeight.value - topOffset.value + triggerThreshold;
  };

  /**
   * 更新所有尺寸
   */
  const updateDimensions = async (): Promise<void> => {
    await calculateHeaderHeights();
    await calculateFilterDimensions();
  };

  /**
   * 初始化吸顶功能
   */
  const initStickyFilter = async (): Promise<void> => {
    try {
      await updateDimensions();

      // 添加滚动监听
      document.body.addEventListener('scroll', handleScroll, { passive: true });
      window.addEventListener('resize', updateDimensions, { passive: true });

      // 初始状态检查
      updateScrollState();
    } catch (error) {
      console.error('筛选器吸顶功能初始化失败:', error);
    }
  };

  /**
   * 销毁吸顶功能
   */
  const destroyStickyFilter = (): void => {
    document.body.removeEventListener('scroll', handleScroll);
    document.body.removeEventListener('resize', updateDimensions);
  };

  return {
    filterContainerRef,
    filterContentRef,
    isSticky,
    filterHeight,
    originalWidth,
    originalLeft,
    topOffset,
    scrollTop,
    stickyThreshold,
    initStickyFilter,
    destroyStickyFilter,
    updateDimensions,
  };
}
