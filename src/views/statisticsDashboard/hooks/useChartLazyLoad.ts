/**
 * 图表懒加载Hook
 * 实现图表组件的懒加载，提升页面初始加载性能
 */

import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';

/**
 * 懒加载状态
 */
interface LazyLoadState {
  /** 是否已进入视口 */
  isIntersecting: boolean;
  /** 是否已加载 */
  isLoaded: boolean;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 加载错误 */
  error: string | null;
}

/**
 * 懒加载配置
 */
interface LazyLoadOptions {
  /** 根边距，用于提前触发加载 */
  rootMargin?: string;
  /** 交叉比例阈值 */
  threshold?: number | number[];
  /** 是否只触发一次 */
  once?: boolean;
  /** 加载延迟（毫秒） */
  delay?: number;
}

/**
 * 图表懒加载Hook
 */
export function useChartLazyLoad(options: LazyLoadOptions = {}) {
  const { rootMargin = '50px', threshold = 0.1, once = true, delay = 0 } = options;

  // 目标元素引用
  const targetRef = ref<HTMLElement>();

  // 懒加载状态
  const state = reactive<LazyLoadState>({
    isIntersecting: false,
    isLoaded: false,
    isLoading: false,
    error: null,
  });

  // Intersection Observer 实例
  let observer: IntersectionObserver | null = null;

  /**
   * 开始加载
   */
  const startLoading = async () => {
    if (state.isLoading || state.isLoaded) return;

    try {
      state.isLoading = true;
      state.error = null;

      // 添加加载延迟
      if (delay > 0) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      // 标记为已加载
      state.isLoaded = true;
    } catch (error) {
      state.error = error instanceof Error ? error.message : '加载失败';
      console.error('❌ [懒加载] 图表懒加载失败:', error);
    } finally {
      state.isLoading = false;
    }
  };

  /**
   * 重新加载
   */
  const reload = () => {
    state.isLoaded = false;
    state.error = null;
    if (state.isIntersecting) {
      startLoading();
    }
  };

  /**
   * 处理交叉观察
   */
  const handleIntersection = (entries: IntersectionObserverEntry[]) => {
    const entry = entries[0];
    state.isIntersecting = entry.isIntersecting;

    if (entry.isIntersecting && !state.isLoaded && !state.isLoading) {
      startLoading();

      // 如果只触发一次，停止观察
      if (once && observer) {
        observer.disconnect();
      }
    }
  };

  /**
   * 初始化观察器
   */
  const initObserver = () => {
    if (!targetRef.value || !window.IntersectionObserver) {
      // 如果不支持 IntersectionObserver，直接加载
      startLoading();
      return;
    }

    observer = new IntersectionObserver(handleIntersection, {
      rootMargin,
      threshold,
    });

    observer.observe(targetRef.value);
  };

  /**
   * 销毁观察器
   */
  const destroyObserver = () => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
  };

  // 组件挂载时初始化
  onMounted(() => {
    nextTick(() => {
      initObserver();
    });
  });

  // 组件卸载时清理
  onUnmounted(() => {
    destroyObserver();
  });

  return {
    // 引用
    targetRef,

    // 状态
    state,
    isIntersecting: () => state.isIntersecting,
    isLoaded: () => state.isLoaded,
    isLoading: () => state.isLoading,
    hasError: () => !!state.error,

    // 方法
    reload,
    startLoading,
  };
}

/**
 * 图表容器懒加载Hook
 * 专门用于图表容器的懒加载管理
 */
export function useChartContainerLazyLoad() {
  // 已加载的图表ID集合
  const loadedCharts = ref(new Set<string>());

  // 正在加载的图表ID集合
  const loadingCharts = ref(new Set<string>());

  /**
   * 检查图表是否已加载
   */
  const isChartLoaded = (chartId: string): boolean => {
    return loadedCharts.value.has(chartId);
  };

  /**
   * 检查图表是否正在加载
   */
  const isChartLoading = (chartId: string): boolean => {
    return loadingCharts.value.has(chartId);
  };

  /**
   * 标记图表开始加载
   */
  const markChartLoading = (chartId: string) => {
    loadingCharts.value.add(chartId);
  };

  /**
   * 标记图表加载完成
   */
  const markChartLoaded = (chartId: string, chartTitle?: string) => {
    loadingCharts.value.delete(chartId);
    loadedCharts.value.add(chartId);

    // 记录简化的懒加载日志
    const loadedCount = loadedCharts.value.size;
    const totalCount = loadedCount + loadingCharts.value.size;
    console.log(`📊 [懒加载] ${chartTitle || chartId} 加载完成 (${loadedCount}/${totalCount})`);
  };

  /**
   * 重置图表状态
   */
  const resetChart = (chartId: string) => {
    loadingCharts.value.delete(chartId);
    loadedCharts.value.delete(chartId);
  };

  /**
   * 清空所有状态
   */
  const clearAll = () => {
    loadedCharts.value.clear();
    loadingCharts.value.clear();
  };

  /**
   * 获取加载统计
   */
  const getLoadStats = () => {
    return {
      loaded: loadedCharts.value.size,
      loading: loadingCharts.value.size,
      total: loadedCharts.value.size + loadingCharts.value.size,
    };
  };

  return {
    // 状态
    loadedCharts,
    loadingCharts,

    // 方法
    isChartLoaded,
    isChartLoading,
    markChartLoading,
    markChartLoaded,
    resetChart,
    clearAll,
    getLoadStats,
  };
}

/**
 * 图表预加载Hook
 * 用于预加载即将进入视口的图表
 */
export function useChartPreload() {
  const preloadQueue = ref<string[]>([]);
  const preloadedCharts = ref(new Set<string>());

  /**
   * 添加到预加载队列
   */
  const addToPreloadQueue = (chartId: string) => {
    if (!preloadQueue.value.includes(chartId) && !preloadedCharts.value.has(chartId)) {
      preloadQueue.value.push(chartId);
    }
  };

  /**
   * 执行预加载
   */
  const executePreload = async (chartId: string) => {
    if (preloadedCharts.value.has(chartId)) return;

    try {
      // 这里可以预加载图表数据或资源
      // 例如：预加载图表数据、图片资源等
      await new Promise((resolve) => setTimeout(resolve, 100));

      preloadedCharts.value.add(chartId);

      // 从队列中移除
      const index = preloadQueue.value.indexOf(chartId);
      if (index > -1) {
        preloadQueue.value.splice(index, 1);
      }
    } catch (error) {
      console.error('图表预加载失败:', chartId, error);
    }
  };

  /**
   * 处理预加载队列
   */
  const processPreloadQueue = async () => {
    const queue = [...preloadQueue.value];
    await Promise.all(queue.map((chartId) => executePreload(chartId)));
  };

  return {
    preloadQueue,
    preloadedCharts,
    addToPreloadQueue,
    executePreload,
    processPreloadQueue,
  };
}
