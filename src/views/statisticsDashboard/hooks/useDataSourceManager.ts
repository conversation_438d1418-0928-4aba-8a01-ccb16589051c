/**
 * 数据源管理Hook
 * 统一管理数据源映射和数据获取逻辑，替代mock/data.ts中的数据管理
 */

import { reactive, computed, readonly } from 'vue';
import type { ChartDataItem } from '../types/statisticDashboard';

// 导入具体的数据源
import { utmSourceData, utmMediumData, clueEffectivenessData } from '../mock/clueSourceMock';
import { followUpOnCluesData, firstFollowUpTimeAnalysisData, firstResponseTimeoutData } from '../mock/followUpOnCluesMock';

// 导入API和数据转换工具
import type { AllClueSourceResponseType, commonQueryParams } from '../api';
import { queryAllClueSource, queryValidClueSource, queryAllClueSourceSecond, queryValidClueSourceSecond } from '../api';
import { transformApiDataToChartData, transformSecondApiDataToChartData, validateTransformedData, getDataStatistics } from '../utils/dataTransform';
import { sourceOfCluesChartConfigs } from '../config/clueSourceChartConfig';

/**
 * 数据源管理Hook
 */
export function useDataSourceManager() {
  // 数据源映射存储
  const dataSourceMap = reactive<Record<string, ChartDataItem[]>>({});

  // 数据源元数据
  const dataSourceMeta = reactive<
    Record<
      string,
      {
        name: string;
        description: string;
        category: string;
        lastUpdated: Date;
        size: number;
      }
    >
  >({});

  /**
   * 初始化默认数据源
   */
  const initializeDefaultDataSources = () => {
    const defaultDataSources = {
      // 线索相关数据源
      sourceOfAllClues: {
        data: sourceOfCluesChartConfigs,
        meta: {
          name: '全量线索来源',
          description: '所有线索的来源分布数据',
          category: 'clue',
          lastUpdated: new Date(),
          size: sourceOfCluesChartConfigs.length,
        },
      },
      sourceOfEffectiveClues: {
        data: sourceOfCluesChartConfigs,
        meta: {
          name: '有效线索来源',
          description: '有效线索的来源分布数据',
          category: 'clue',
          lastUpdated: new Date(),
          size: sourceOfCluesChartConfigs.length,
        },
      },

      // UTM相关数据源
      utmSource: {
        data: utmSourceData,
        meta: {
          name: 'UTM来源',
          description: 'UTM来源统计数据',
          category: 'utm',
          lastUpdated: new Date(),
          size: utmSourceData.length,
        },
      },
      utmMedium: {
        data: utmMediumData,
        meta: {
          name: 'UTM媒介',
          description: 'UTM媒介统计数据',
          category: 'utm',
          lastUpdated: new Date(),
          size: utmMediumData.length,
        },
      },

      // 线索效果数据源
      clueEffectiveness: {
        data: clueEffectivenessData,
        meta: {
          name: '线索有效性',
          description: '线索有效性分析数据',
          category: 'effectiveness',
          lastUpdated: new Date(),
          size: clueEffectivenessData.length,
        },
      },

      // 跟进相关数据源
      followUpOnClues: {
        data: followUpOnCluesData,
        meta: {
          name: '线索跟进状态',
          description: '线索跟进状态统计数据',
          category: 'followUp',
          lastUpdated: new Date(),
          size: followUpOnCluesData.length,
        },
      },
      firstFollowUpTimeAnalysis: {
        data: firstFollowUpTimeAnalysisData,
        meta: {
          name: '首次跟进时长分析',
          description: '首次跟进时长分析数据',
          category: 'followUp',
          lastUpdated: new Date(),
          size: firstFollowUpTimeAnalysisData.length,
        },
      },
      firstResponseTimeout: {
        data: firstResponseTimeoutData,
        meta: {
          name: '首次响应超时',
          description: '首次响应超时统计数据',
          category: 'followUp',
          lastUpdated: new Date(),
          size: firstResponseTimeoutData.length,
        },
      },
    };

    // 注册所有数据源
    Object.entries(defaultDataSources).forEach(([key, { data, meta }]) => {
      dataSourceMap[key] = data as ChartDataItem[];
      dataSourceMeta[key] = meta;
    });
  };

  /**
   * 获取数据源数据
   */
  const getDataSource = (dataSourceKey: string): ChartDataItem[] => {
    return dataSourceMap[dataSourceKey] || [];
  };

  /**
   * 设置数据源数据
   */
  const setDataSource = (dataSourceKey: string, data: ChartDataItem[], meta?: Partial<(typeof dataSourceMeta)[string]>) => {
    dataSourceMap[dataSourceKey] = data;

    if (meta) {
      dataSourceMeta[dataSourceKey] = {
        name: meta.name || dataSourceKey,
        description: meta.description || '',
        category: meta.category || 'custom',
        lastUpdated: new Date(),
        size: data.length,
        ...meta,
      };
    }
  };

  /**
   * 更新数据源数据
   */
  const updateDataSource = (dataSourceKey: string, data: ChartDataItem[]) => {
    if (dataSourceMap[dataSourceKey]) {
      dataSourceMap[dataSourceKey] = data;

      if (dataSourceMeta[dataSourceKey]) {
        dataSourceMeta[dataSourceKey].lastUpdated = new Date();
        dataSourceMeta[dataSourceKey].size = data.length;
      }
    }
  };

  /**
   * 从API获取全量线索数据并转换格式
   */
  const fetchAndTransformAllClueData = async (params: commonQueryParams) => {
    try {
      const response = await queryAllClueSource(params);
      const apiData = response as AllClueSourceResponseType;

      // 转换API数据为图表格式
      const transformedData = transformApiDataToChartData(apiData);

      // 验证转换后的数据
      const validationResult = validateTransformedData(transformedData);

      if (validationResult.isValid) {
        // 更新数据源（包括空数据情况）
        setDataSource('sourceOfAllClues', transformedData as any, {
          name: '全量线索来源',
          description: validationResult.isEmpty ? '从API获取的全量线索来源数据（暂无数据）' : '从API获取的全量线索来源数据',
          category: 'clue',
        });

        if (validationResult.isEmpty) {
          console.info('全量线索数据为空');
        } else {
          console.log('全量线索数据获取成功:', getDataStatistics(transformedData));
        }
        return transformedData;
      } else {
        console.error('数据转换验证失败:', validationResult.error);
        throw new Error(`数据验证失败: ${validationResult.error}`);
      }
    } catch (error) {
      console.error('获取全量线索数据失败:', error);
      return [];
    }
  };

  /**
   * 从API获取有效线索数据并转换格式
   */
  const fetchAndTransformValidClueData = async (params: commonQueryParams) => {
    try {
      const response = await queryValidClueSource(params);
      const apiData = response as AllClueSourceResponseType;

      // 转换API数据为图表格式
      const transformedData = transformApiDataToChartData(apiData);

      // 验证转换后的数据
      const validationResult = validateTransformedData(transformedData);

      if (validationResult.isValid) {
        // 更新数据源（包括空数据情况）
        setDataSource('sourceOfEffectiveClues', transformedData as any, {
          name: '有效线索来源',
          description: validationResult.isEmpty ? '从API获取的有效线索来源数据（暂无数据）' : '从API获取的有效线索来源数据',
          category: 'clue',
        });

        if (validationResult.isEmpty) {
          console.info('有效线索数据为空');
        } else {
          console.log('有效线索数据获取成功:', getDataStatistics(transformedData));
        }
        return transformedData;
      } else {
        console.error('数据转换验证失败:', validationResult.error);
        throw new Error(`数据验证失败: ${validationResult.error}`);
      }
    } catch (error) {
      console.error('获取有效线索数据失败:', error);
      return {};
    }
  };

  /**
   * 从API获取二级下探数据并转换格式
   */
  const fetchAndTransformSecondLevelData = async (params: commonQueryParams, parentSourceId: string, isValidClue: boolean = false) => {
    try {
      const queryParams = { ...params, oneSourceId: parentSourceId };
      const response = isValidClue ? await queryValidClueSourceSecond(queryParams) : await queryAllClueSourceSecond(queryParams);

      const apiData = response as AllClueSourceResponseType;

      // 转换API数据为图表格式
      const transformedData = transformSecondApiDataToChartData(apiData, parentSourceId);

      console.log('二级下探数据获取成功:', {
        parentSourceId,
        isValidClue,
        dataCount: transformedData.length,
      });

      return transformedData;
    } catch (error) {
      console.error('获取二级下探数据失败:', error);
      return [];
    }
  };

  /**
   * 删除数据源
   */
  const removeDataSource = (dataSourceKey: string) => {
    delete dataSourceMap[dataSourceKey];
    delete dataSourceMeta[dataSourceKey];
  };

  /**
   * 获取数据源元数据
   */
  const getDataSourceMeta = (dataSourceKey: string) => {
    return dataSourceMeta[dataSourceKey];
  };

  /**
   * 获取所有数据源键名
   */
  const getAllDataSourceKeys = (): string[] => {
    return Object.keys(dataSourceMap);
  };

  /**
   * 按分类获取数据源
   */
  const getDataSourcesByCategory = (category: string): Record<string, ChartDataItem[]> => {
    const result: Record<string, ChartDataItem[]> = {};

    Object.entries(dataSourceMeta).forEach(([key, meta]) => {
      if (meta.category === category) {
        result[key] = dataSourceMap[key];
      }
    });

    return result;
  };

  /**
   * 清空所有数据源
   */
  const clearAllDataSources = () => {
    Object.keys(dataSourceMap).forEach((key) => delete dataSourceMap[key]);
    Object.keys(dataSourceMeta).forEach((key) => delete dataSourceMeta[key]);
  };

  /**
   * 重置为默认数据源
   */
  const resetToDefaults = () => {
    clearAllDataSources();
    initializeDefaultDataSources();
  };

  // 计算属性：数据源统计
  const dataSourceStats = computed(() => {
    const categories = new Set(Object.values(dataSourceMeta).map((meta) => meta.category));
    const totalSize = Object.values(dataSourceMeta).reduce((sum, meta) => sum + meta.size, 0);

    return {
      totalDataSources: Object.keys(dataSourceMap).length,
      totalCategories: categories.size,
      totalDataPoints: totalSize,
      categories: Array.from(categories),
    };
  });

  // 计算属性：按分类分组的数据源
  const dataSourcesByCategory = computed(() => {
    const grouped: Record<string, string[]> = {};

    Object.entries(dataSourceMeta).forEach(([key, meta]) => {
      if (!grouped[meta.category]) {
        grouped[meta.category] = [];
      }
      grouped[meta.category].push(key);
    });

    return grouped;
  });

  // 初始化默认数据源
  initializeDefaultDataSources();

  return {
    // 状态（只读）
    dataSourceMap: readonly(dataSourceMap),
    dataSourceMeta: readonly(dataSourceMeta),
    dataSourceStats,
    dataSourcesByCategory,

    // 基础方法
    getDataSource,
    setDataSource,
    updateDataSource,
    removeDataSource,
    getDataSourceMeta,
    getAllDataSourceKeys,
    getDataSourcesByCategory,
    clearAllDataSources,
    resetToDefaults,

    // API数据获取方法
    fetchAndTransformAllClueData,
    fetchAndTransformValidClueData,
    fetchAndTransformSecondLevelData,
  };
}
