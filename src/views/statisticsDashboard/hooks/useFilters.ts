/**
 * 筛选器管理 Hook
 * 提供筛选条件的管理、验证和应用功能
 */

import { ref, computed, readonly, reactive, onMounted } from 'vue';
import { FormSchema } from '/@/components/Table';
import { useI18n } from '/@/hooks/web/useI18n';
import dayjs from 'dayjs';
import { useUserStore } from '/@/store/modules/user';
import { useForm } from '/@/components/Form';
import { awaitTo } from '@ruqi/utils-admin';
import { queryRegionDetailsApi } from '../../system/depart/depart.api';
import { queryAreaHaveAuth } from '/@/api/common/api';

/**
 * 统计周期配置接口
 */
interface StatPeriodConfig {
  displayFormat: string;
  pickerType: string | undefined;
  defaultRange: {
    subtract: number;
    unit: string;
  };
}

/**
 * 筛选器管理
 */
export function useFilters(opt: { enableUserDataAuth: boolean }) {
  const { enableUserDataAuth } = opt;

  const { t } = useI18n('common');

  const { userInfo } = useUserStore();

  // 统一的输出格式配置
  const OUTPUT_FORMAT_CONFIG = {
    // 可选格式类型
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    DATE: 'YYYY-MM-DD',
    TIMESTAMP: 'timestamp', // 时间戳格式(秒)
  };

  // 显示格式配置
  const DISPLAY_FORMAT_CONFIG = {
    UNIFIED: 'YYYY-MM-DD', // 统一显示格式
    ADAPTIVE: 'adaptive', // 自适应显示格式（根据统计周期变化）
  };

  // 当前使用的输出格式（可配置）
  const outputFormat = ref<string>(OUTPUT_FORMAT_CONFIG.DATE);

  // 当前使用的显示格式模式（可配置）
  const displayFormatMode = ref<string>(DISPLAY_FORMAT_CONFIG.UNIFIED);

  // 当前选择的统计周期
  const currentStatPeriod = ref<string>('day');

  // 统计周期配置映射
  const STAT_PERIOD_CONFIG: Record<string, StatPeriodConfig> = {
    day: {
      displayFormat: 'YYYY-MM-DD',
      pickerType: undefined, // 默认日期选择
      defaultRange: { subtract: 6, unit: 'day' }, // 近7天
    },
    week: {
      displayFormat: 'YYYY-wo',
      pickerType: 'week',
      defaultRange: { subtract: 3, unit: 'week' }, // 近4周
    },
    month: {
      displayFormat: 'YYYY-MM',
      pickerType: 'month',
      defaultRange: { subtract: 5, unit: 'month' }, // 近6个月
    },
    quarter: {
      displayFormat: 'YYYY-[Q]Q',
      pickerType: 'quarter',
      defaultRange: { subtract: 9, unit: 'month' }, // 近4个季度 (每个季度3个月)
    },
    year: {
      displayFormat: 'YYYY',
      pickerType: 'year',
      defaultRange: { subtract: 4, unit: 'year' }, // 近5年
    },
  };

  const formSchemas = computed<FormSchema[]>(() => {
    const pickerConfig = getPickerConfig(currentStatPeriod.value);

    const schemas: FormSchema[] = [
      {
        field: 'statPeriod',
        label: t('statPeriod'),
        component: 'Select',
        colProps: { span: 8 },
        labelWidth: '100%',
        required: true,
        componentProps: {
          placeholder: t('chooseText'),
          options: [
            { label: t('time_days'), value: 'day' },
            { label: t('_week'), value: 'week' },
            { label: t('month'), value: 'month' },
            { label: t('quarter'), value: 'quarter' },
            { label: t('year'), value: 'year' },
          ],
        },
        defaultValue: 'day',
      },
      {
        field: 'periodRange',
        label: t('periodRange'),
        component: 'RangePicker',
        colProps: { span: 8 },
        labelWidth: '100%',
        required: true,
        componentProps: {
          picker: pickerConfig.picker,
          format: pickerConfig.format, // 显示格式
          valueFormat: pickerConfig.valueFormat, // 提交格式（统一格式）
          valueType: 'array', // 关键配置：保持数组格式，不转换为字符串
          style: { width: '100%' },
        },
        defaultValue: getPeriodRangeDefaultValue(currentStatPeriod.value),
      },
      {
        field: 'regionCode',
        label: t('region_text'),
        component: 'Select',
        colProps: { span: 8 },
        labelWidth: '100%',
        required: false,
        componentProps: {
          placeholder: t('chooseText'),
          options: baseData.regionOptions,
          showSearch: true,
          fieldNames: {
            label: 'regionName',
            value: 'regionCode',
          },
        },
        ifShow: enableUserDataAuth,
      },
      {
        field: 'countryCode',
        label: t('country'),
        component: 'Select',
        colProps: { span: 8 },
        labelWidth: '100%',
        required: false,
        componentProps: {
          placeholder: t('chooseText'),
          options: [],
          showSearch: true,
          fieldNames: {
            label: 'countryName',
            value: 'countryCode',
          },
        },
        ifShow: false,
      },
      {
        field: 'provinceCode',
        label: t('provinceName'),
        component: 'Select',
        colProps: { span: 8 },
        labelWidth: '100%',
        required: false,
        componentProps: {
          placeholder: t('chooseText'),
          options: [],
          showSearch: true,
          fieldNames: {
            label: 'provinceName',
            value: 'provinceCode',
          },
        },
        ifShow: false,
      },
      {
        field: 'cityCode',
        label: t('city'),
        component: 'Select',
        colProps: { span: 8 },
        labelWidth: '100%',
        required: false,
        componentProps: {
          placeholder: t('chooseText'),
          options: [],
          showSearch: true,
          fieldNames: {
            label: 'cityName',
            value: 'cityCode',
          },
        },
        ifShow: false,
      },
    ];

    return schemas;
  });

  const [registerForm, formMethods] = useForm({
    schemas: formSchemas,
    showActionButtonGroup: true,
    showSubmitButton: true,
    showResetButton: true,
    layout: 'vertical',
    labelAlign: 'left',
  });

  const filters = computed(() => formMethods?.getFieldsValue() ?? {});

  /**
   * 根据统计周期计算默认的时间范围
   */
  const getDefaultDateRange = (statPeriod: string) => {
    const now = dayjs();
    const config = STAT_PERIOD_CONFIG[statPeriod] || STAT_PERIOD_CONFIG.day;
    const { subtract, unit } = config.defaultRange;

    return [now.subtract(subtract, unit as any), now];
  };

  /**
   * 根据统计周期获取picker配置
   */
  const getPickerConfig = (statPeriod: string) => {
    const config = STAT_PERIOD_CONFIG[statPeriod] || STAT_PERIOD_CONFIG.day;

    // 显示格式（用户看到的格式）
    const getDisplayFormat = () => {
      if (displayFormatMode.value === DISPLAY_FORMAT_CONFIG.UNIFIED) {
        return DISPLAY_FORMAT_CONFIG.UNIFIED; // 统一使用 YYYY-MM-DD
      }
      return config.displayFormat; // 自适应格式（根据统计周期变化）
    };

    // 值格式（提交给后端的格式）
    const getValueFormat = () => {
      if (outputFormat.value === OUTPUT_FORMAT_CONFIG.TIMESTAMP) {
        return 'X'; // dayjs的时间戳格式
      }
      return outputFormat.value; // 统一的日期时间格式
    };

    return {
      picker: config.pickerType,
      format: getDisplayFormat(),
      valueFormat: getValueFormat(),
    };
  };

  /**
   * 获取periodRange的默认值
   */
  const getPeriodRangeDefaultValue = (statPeriod: string) => {
    return getDefaultDateRange(statPeriod);
  };

  /**
   * 设置输出格式
   * @param format 输出格式类型
   */
  const setOutputFormat = (format: string) => {
    outputFormat.value = format;
  };

  /**
   * 获取当前输出格式
   */
  const getOutputFormat = () => {
    return outputFormat.value;
  };

  /**
   * 设置显示格式模式
   * @param mode 显示格式模式
   */
  const setDisplayFormatMode = (mode: string) => {
    displayFormatMode.value = mode;
  };

  /**
   * 获取当前显示格式模式
   */
  const getDisplayFormatMode = () => {
    return displayFormatMode.value;
  };

  /** 所有基础数据(大区、国家、州/省、城市、门店) */
  const baseData = reactive<Record<string, Record<string, string>[]>>({
    regionOptions: [],
    countryOptions: [],
    provinceOptions: [],
    cityOptions: [],
  });

  const handleUserDataAuth = async () => {
    const [_err, result] = await awaitTo(Promise.all([queryAreaHaveAuth(userInfo!.userId as string), queryRegionDetailsApi()]));
    const [authData = {}, allBaseData] = result || [];
    console.log('userInfo', userInfo);
    console.log('allBaseData', allBaseData);
    // 1. 先对大区和国家数据进行筛选过滤出已有权限的数据。
    // 2. 判断用户的数据权限里是否存在大区且只有一条数据，如果有直接赋值，然后更新国家数据源
    // 3. 判断用户的数据权限里是否存在国家且只有一条数据，如果有直接赋值
    // 4. 判断账户类型，如果是厂端，判断用户的数据权限里是否存在州/省且只有一条数据，如果有直接赋值，更新州/省/数据源
    // 5. 判断用户的数据权限里是否存在城市且只有一条数据，如果有直接赋值，然后更新城市数据源
    // 6. 门店判断 todo
    // 7. 如果是总代&子公司，经销商判断 todo
    // 8. 如果是店端，门店判断 todo
    // 9. 每个层级的需要有数据联动

    /**
     * 1. 获取当前登陆用户的各数据权限集合（大区、国家/地区、州/省、城市）。
     * cityAuthList 用户城市权限数据
     * countryAuthList 用户国家权限数据
     * provinceAuthList 用户州/省权限数据
     * regionCode 用户大区权限数据, 只有一个
     */
    const { regionCode = '', accountCategory } = userInfo ?? {};
    console.log('accountCategory', accountCategory);
    const { cityAuthList = [], countryAuthList = [], provinceAuthList = [] } = authData;

    console.log('authData', authData);

    // 2. 对大区和国家数据进行过滤筛选出有权限的数据
    allBaseData?.forEach((item: Record<string, any>) => {
      const { regionName, regionCode: _regionCode, countries } = item ?? {};

      if (regionCode === _regionCode) {
        // 大区数据
        baseData.regionOptions.push({ regionName, regionCode: _regionCode });

        countries?.forEach((country: Record<string, any>) => {
          const { countryCode, countryName, provinces, hasProvince, cities } = country ?? {};
          if (countryAuthList?.includes(countryCode)) {
            baseData.countryOptions.push({ countryCode, countryName, regionCode: _regionCode, hasProvince });
          }
          // 非厂端账户只处理到国家/地区层级
          // if (Number(accountCategory) !== AccountTypeEnum.FACTORY_END) {
          //   return;
          // }
          if (hasProvince && provinces?.length) {
            provinces.forEach((province: Record<string, any>) => {
              const { provinceName, provinceCode } = province ?? {};
              if (provinceAuthList?.includes(provinceCode)) {
                baseData.provinceOptions.push({
                  provinceCode,
                  provinceName,
                  countryCode,
                });
                // 如果州/省下存在城市数据，继续处理
                if (province.cities?.length) {
                  province.cities.forEach((city: Record<string, any>) => {
                    const { cityName, cityCode } = city ?? {};
                    if (cityAuthList?.includes(cityCode)) {
                      baseData.cityOptions.push({
                        cityCode,
                        cityName,
                        provinceCode,
                      });
                    }
                  });
                }
              }
            });
          } else {
            cities?.forEach((city: Record<string, any>) => {
              const { cityCode, cityName } = city ?? {};
              if (cityAuthList?.includes(cityCode)) {
                baseData.cityOptions.push({
                  cityCode,
                  cityName,
                  countryCode,
                });
              }
            });
          }
        });
      }
    });

    const params = {} as Record<string, string | number>;

    // 更新大区数据源
    formMethods.updateSchema([
      {
        field: 'regionCode',
        componentProps: {
          options: baseData.regionOptions,
        },
      },
    ]);

    // 赋值大区并更新国家数据源
    params.regionCode = regionCode;
    formMethods.updateSchema({
      field: 'countryCode',
      componentProps: {
        options: baseData.countryOptions,
      },
      ifShow: true,
    });

    // 赋值国家 当前大区下的国家只有一条数据的时候
    if (countryAuthList?.length === 1) {
      const countryCode = countryAuthList[0];
      params.countryCode = countryCode;
      const { hasProvince } = baseData.countryOptions.find((country) => country.countryCode === countryCode) ?? {};
      if (hasProvince) {
        // 更新州/省 数据源
        formMethods.updateSchema({
          field: 'provinceCode',
          componentProps: {
            options: baseData.provinceOptions,
          },
          ifShow: baseData.provinceOptions.length > 0,
        });
        // 赋值州/省数据，当前国家下只有一条州/省数据的时候,provinceAuthList是全部的州/省数据
        const provinceCodes = baseData.provinceOptions
          .filter((province) => province.countryCode === countryCode)
          .map((item) => item.provinceCode)
          .filter((item) => provinceAuthList.includes(item));
        if (provinceCodes.length === 1) {
          const provinceCode = provinceCodes[0];
          params.provinceCode = provinceCode;
          // 更新城市 数据源
          formMethods.updateSchema({
            field: 'cityCode',
            componentProps: {
              options: baseData.cityOptions,
            },
            ifShow: baseData.cityOptions.length > 0,
          });
          // 赋值城市数据，当前州/省下城市只有一条数据的时候
          const cityCodes = baseData.cityOptions
            .filter((city) => city.provinceCode === provinceCode)
            .map((item) => item.cityCode)
            .filter((item) => cityAuthList.includes(item));
          if (cityCodes.length === 1) {
            const cityCode = cityCodes[0];
            params.cityCode = cityCode;
          }
        }
      } else {
        console.log('********');
        // 更新城市 数据源
        formMethods.updateSchema({
          field: 'cityCode',
          componentProps: {
            options: baseData.cityOptions,
          },
          ifShow: baseData.cityOptions.length > 0,
        });
      }
    }

    // 非厂端账户只处理到国家/地区层级
    // if (Number(accountCategory) !== AccountTypeEnum.FACTORY_END) {
    //   return;
    // }
    console.log('provinceAuthList', provinceAuthList);

    // 赋值州/省，当前国家/地区下的州/省只有一条数据的时候
    // if (provinceAuthList?.length === 1) {
    //   const provinceCode = provinceAuthList[0];
    //   params.provinceCode = provinceCode;
    //   formMethods.updateSchema({
    //     field: 'cityCode',
    //     componentProps: {
    //       options: baseData.cityOptions,
    //     },
    //     ifShow: true
    //   });
    // }

    // 赋值城市，当前州/省下城市只有一条数据的时候
    // if (cityAuthList?.length === 1) {
    //   const cityCode = cityAuthList[0];
    //   params.cityCode = cityCode;
    // }

    formMethods?.setFieldsValue(params);
  };

  const handleFieldChange = (field: string, value: string) => {
    const fieldValues = formMethods.getFieldsValue() ?? {};
    // 当统计周期变化时，更新时间范围选择器的配置和默认值
    if (field === 'statPeriod' && value) {
      const pickerConfig = getPickerConfig(value);
      const defaultRange = getPeriodRangeDefaultValue(value);

      // 更新periodRange字段的配置
      formMethods.updateSchema({
        field: 'periodRange',
        componentProps: {
          picker: pickerConfig.picker,
          format: pickerConfig.format, // 显示格式
          valueFormat: pickerConfig.valueFormat, // 提交格式（统一格式）
          valueType: 'array', // 保持数组格式
        },
      });

      // 设置默认值
      formMethods.setFieldsValue({
        periodRange: defaultRange,
      });
      return;
    }
    if (field === 'regionCode') {
      formMethods.updateSchema({
        field: 'countryCode',
        componentProps: {
          options: !value ? [] : baseData.countryOptions.filter((country) => country.regionCode === value),
        },
        ifShow: true,
      });
      !value &&
        formMethods.setFieldsValue({
          countryCode: undefined,
        });
      return;
    }
    if (field === 'countryCode') {
      const countryCode = value;
      const findCountryValue = baseData.countryOptions.find((country) => country.countryCode === countryCode) ?? {};
      const { hasProvince } = findCountryValue;
      if (hasProvince) {
        formMethods.updateSchema({
          field: 'provinceCode',
          componentProps: {
            options: !value ? [] : baseData.provinceOptions.filter((province) => province.countryCode === countryCode),
          },
          ifShow: baseData.provinceOptions.length > 0,
        });
        !value &&
          formMethods.setFieldsValue({
            provinceCode: '',
          });
      } else {
        formMethods.updateSchema({
          field: 'cityCode',
          componentProps: {
            options: !value ? [] : baseData.cityOptions.filter((city) => city.countryCode === countryCode),
          },
          ifShow: baseData.cityOptions.length > 0,
        });
        !value &&
          formMethods.setFieldsValue({
            cityCode: '',
          });
      }
      return;
    }
    if (field === 'provinceCode') {
      formMethods.updateSchema({
        field: 'cityCode',
        componentProps: {
          options: !value ? [] : baseData.cityOptions.filter((city) => city.provinceCode === value),
        },
        ifShow: baseData.cityOptions.length > 0,
      });
      fieldValues.cityCode &&
        formMethods.setFieldsValue({
          cityCode: undefined,
        });
      return;
    }
  };

  onMounted(() => {
    if (enableUserDataAuth) {
      // 启用用户数据权限
      handleUserDataAuth();
    }
  });

  /**
   * 获取当前筛选条件值
   */
  const getCurrentFilters = () => {
    return formMethods.getFieldsValue();
  };

  /**
   * 将筛选条件转换为API查询参数
   */
  const transformFiltersToQueryParams = () => {
    const currentFilters = getCurrentFilters();
    const { statPeriod, periodRange, regionCode, countryCode, provinceCode, cityCode } = currentFilters;

    // 转换统计维度
    const statisticalDimensionMap = {
      day: 'DAY',
      week: 'WEEK',
      month: 'MONTH',
      quarter: 'QUARTER',
      year: 'YEAR',
    };

    const [startDate, endDate] = periodRange?.split(',') ?? [];

    const queryParams: any = {
      startDate: startDate || dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
      endDate: endDate || dayjs().format('YYYY-MM-DD'),
      statisticalDimension: statisticalDimensionMap[statPeriod] || 'DAY',
      regionCenterCode: userInfo?.regionCenterCode,
    };

    // 添加地区相关参数（如果有选择）
    if (regionCode) queryParams.regionCode = regionCode;
    if (countryCode) queryParams.countryCode = countryCode;
    if (provinceCode) queryParams.provinceCode = provinceCode;
    if (cityCode) queryParams.cityCode = cityCode;

    return queryParams;
  };

  /**
   * 获取默认的查询参数（用于初始加载）
   */
  const getDefaultQueryParams = () => {
    const defaultRange = getDefaultDateRange(currentStatPeriod.value);

    return {
      startDate: defaultRange[0],
      endDate: defaultRange[1],
      statisticalDimension: 'DAY',
      regionCenterCode: userInfo?.regionCenterCode,
    };
  };

  return {
    currentStatPeriod: readonly(currentStatPeriod),
    filters,

    // 配置项
    formSchemas,
    OUTPUT_FORMAT_CONFIG,
    DISPLAY_FORMAT_CONFIG,

    // 表单配置
    registerForm,
    formMethods,

    // 方法
    getPeriodRangeDefaultValue,
    getDefaultDateRange,
    getPickerConfig,
    setOutputFormat,
    getOutputFormat,
    setDisplayFormatMode,
    getDisplayFormatMode,
    handleFieldChange,

    // 新增方法
    getCurrentFilters,
    transformFiltersToQueryParams,
    getDefaultQueryParams,
  };
}
