/**
 * 统一数据加载管理器
 * 整合策略系统到现有架构，提供统一的图表数据加载接口
 */

import { reactive } from 'vue';
import { message } from 'ant-design-vue';
import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import type { commonQueryParams } from '../api';
import { useTabConfigManager } from './useTabConfigManager';
import { ChartDataLoadingStrategyFactory, type IChartDataLoadingStrategy } from '../strategies/ChartDataLoadingStrategy';

/**
 * 图表加载状态管理接口
 */
interface ChartLoadingState {
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  strategy: string | null;
}

/**
 * 统一数据加载管理器
 */
export function useUnifiedDataLoader() {
  const tabConfigManager = useTabConfigManager();

  // 加载状态管理
  const loadingStates = reactive<Record<string, ChartLoadingState>>({});

  // 策略缓存
  const strategyCache = new Map<string, IChartDataLoadingStrategy>();

  /**
   * 设置图表加载状态
   */
  const setLoadingState = (chartId: string, loading: boolean, error: string | null = null, strategy?: string) => {
    if (!loadingStates[chartId]) {
      loadingStates[chartId] = {
        loading: false,
        error: null,
        lastUpdated: null,
        strategy: null,
      };
    }

    loadingStates[chartId].loading = loading;
    loadingStates[chartId].error = error;
    if (strategy) {
      loadingStates[chartId].strategy = strategy;
    }

    if (!loading && !error) {
      loadingStates[chartId].lastUpdated = new Date();
    }
  };

  /**
   * 获取或创建策略实例
   */
  const getStrategy = (chartConfig: ChartConfig): IChartDataLoadingStrategy => {
    const cacheKey = `${chartConfig.id}_${chartConfig.type}`;

    if (!strategyCache.has(cacheKey)) {
      const strategy = ChartDataLoadingStrategyFactory.createStrategy(chartConfig);
      strategyCache.set(cacheKey, strategy);
      console.log(`🎯 为图表 ${chartConfig.id} 创建策略: ${strategy.strategyType}`);
    }

    return strategyCache.get(cacheKey)!;
  };

  /**
   * 统一加载图表数据
   * 🔥 核心方法：自动获取筛选参数并调用对应策略
   */
  const loadChartData = async (chartId: string, customParams?: Partial<commonQueryParams>): Promise<ChartDataItem[]> => {
    const chartConfig = tabConfigManager.getChartConfig(chartId);
    if (!chartConfig) {
      throw new Error(`图表配置未找到: ${chartId}`);
    }

    // 获取策略
    const strategy = getStrategy(chartConfig);

    // 设置加载状态
    setLoadingState(chartId, true, null, strategy.strategyType);

    try {
      console.log(`🚀 开始加载图表数据 [${strategy.strategyType}]: ${chartId}`);

      // 🔥 策略会自动通过装饰器获取筛选参数
      const data = await strategy.loadData(chartConfig, customParams);

      // 🔥 自动更新图表配置
      await updateChartConfigWithData(chartConfig, data);

      // 清除加载状态
      setLoadingState(chartId, false);

      console.log(`✅ 图表数据加载成功: ${chartId}, 数据条数: ${data.length}`);
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setLoadingState(chartId, false, errorMessage);
      console.error(`❌ 图表数据加载失败: ${chartId}`, error);
      message.error(`${chartConfig.title} 数据加载失败`);
      throw error;
    }
  };

  /**
   * 刷新图表数据
   */
  const refreshChartData = async (chartId: string, customParams?: Partial<commonQueryParams>): Promise<ChartDataItem[]> => {
    const chartConfig = tabConfigManager.getChartConfig(chartId);
    if (!chartConfig) {
      throw new Error(`图表配置未找到: ${chartId}`);
    }

    const strategy = getStrategy(chartConfig);
    setLoadingState(chartId, true, null, strategy.strategyType);

    try {
      const data = await strategy.refreshData(chartConfig, customParams);
      await updateChartConfigWithData(chartConfig, data);
      setLoadingState(chartId, false);

      console.log(`🔄 图表数据刷新成功: ${chartId}`);
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setLoadingState(chartId, false, errorMessage);
      throw error;
    }
  };

  // /**
  //  * 切换数据源并重新加载数据
  //  */
  // const switchDataSourceAndReload = async (chartId: string, newDataSource: string, queryParams?: Partial<commonQueryParams>) => {
  //   try {
  //     console.log(`开始切换数据源: ${chartId} -> ${newDataSource}`);

  //     // 获取当前图表配置
  //     const currentConfig = tabConfigManager.getChartConfig(chartId);
  //     if (!currentConfig) {
  //       throw new Error(`图表配置未找到: ${chartId}`);
  //     }

  //     // 确定新的标题
  //     const isAllClues = newDataSource === 'sourceOfAllClues';
  //     const newTitle = isAllClues ? '全量线索来源' : '有效线索来源';
  //     const alternativeTitle = isAllClues ? '有效线索来源' : '全量线索来源';
  //     const alternativeDataSource = isAllClues ? 'sourceOfEffectiveClues' : 'sourceOfAllClues';

  //     // 先更新数据源配置和标题
  //     tabConfigManager.updateChartConfig(chartId, {
  //       title: newTitle,
  //       dataSource: newDataSource,
  //       customProps: {
  //         ...currentConfig.customProps,
  //         currentDataSource: newDataSource,
  //         alternativeDataSource: alternativeDataSource,
  //         alternativeTitle: alternativeTitle,
  //         loading: true,
  //         needsAsyncData: true,
  //       },
  //     });

  //     console.log(`配置已更新，开始加载数据: ${newDataSource}`);

  //     // 🔥 使用统一数据加载器重新加载数据，自动选择策略并注入筛选参数
  //     await unifiedLoader.switchDataSourceAndReload(chartId, newDataSource, queryParams);

  //     console.log(`数据源切换完成: ${newDataSource}`);
  //     message.success(`已切换到: ${newTitle}`);
  //   } catch (error) {
  //     console.error('数据源切换失败:', error);
  //     message.error('数据源切换失败');
  //     throw error;
  //   }
  // };

  /**
   * 切换数据源并重新加载
   */
  const switchDataSourceAndReload = async (
    chartId: string,
    newDataSource: string,
    customParams?: Partial<commonQueryParams>
  ): Promise<ChartDataItem[]> => {
    const chartConfig = tabConfigManager.getChartConfig(chartId);
    if (!chartConfig) {
      throw new Error(`图表配置未找到: ${chartId}`);
    }

    const strategy = getStrategy(chartConfig);

    // 检查策略是否支持数据源切换
    if (!strategy.switchDataSource) {
      console.warn(`策略 ${strategy.strategyType} 不支持数据源切换`);
      return await loadChartData(chartId, customParams);
    }

    setLoadingState(chartId, true, null, strategy.strategyType);

    try {
      console.log(`🔄 切换数据源: ${chartId} -> ${newDataSource}`);

      const data = await strategy.switchDataSource(chartConfig, newDataSource, customParams);

      // 确定新的标题
      const isAllClues = newDataSource === 'sourceOfAllClues';
      const newTitle = isAllClues ? '全量线索来源' : '有效线索来源';
      const alternativeTitle = isAllClues ? '有效线索来源' : '全量线索来源';
      const alternativeDataSource = isAllClues ? 'sourceOfEffectiveClues' : 'sourceOfAllClues';

      // 先更新数据源配置和标题
      tabConfigManager.updateChartConfig(chartId, {
        title: newTitle,
        dataSource: newDataSource,
        customProps: {
          ...chartConfig.customProps,
          currentDataSource: newDataSource,
          alternativeDataSource: alternativeDataSource,
          alternativeTitle: alternativeTitle,
          loading: true,
          needsAsyncData: true,
        },
      });
      await updateChartConfigWithData(chartConfig, data, newDataSource);

      setLoadingState(chartId, false);

      console.log(`✅ 数据源切换成功: ${chartId} -> ${newDataSource}`);
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setLoadingState(chartId, false, errorMessage);
      throw error;
    }
  };

  /**
   * 批量加载所有异步图表
   * 🔥 自动获取筛选参数并并发加载
   */
  const loadAllAsyncCharts = async (customParams?: Partial<commonQueryParams>) => {
    const allTabs = tabConfigManager.getAllTabs();
    const asyncCharts: ChartConfig[] = [];

    // 收集所有需要异步加载的图表
    for (const tab of allTabs) {
      for (const group of tab.groups || []) {
        for (const chart of group.chartList || []) {
          if (chart.customProps?.needsAsyncData) {
            asyncCharts.push(chart);
          }
        }
      }
    }

    if (asyncCharts.length === 0) {
      console.log('📋 没有需要异步加载的图表');
      return;
    }

    console.log(`🚀 开始批量加载 ${asyncCharts.length} 个异步图表`);

    // 🔥 并发加载所有图表，自动注入筛选参数
    const loadPromises = asyncCharts.map((chart) =>
      loadChartData(chart.id, customParams).catch((error) => {
        console.error(`图表 ${chart.id} 加载失败:`, error);
        return null;
      })
    );

    try {
      const results = await Promise.allSettled(loadPromises);
      const successful = results.filter((result) => result.status === 'fulfilled').length;
      const failed = results.length - successful;

      if (failed === 0) {
        message.success(`所有图表数据加载完成 (${successful}个)`);
      } else {
        message.warning(`图表数据加载完成，${successful}个成功，${failed}个失败`);
      }

      console.log(`✅ 批量加载完成: ${successful}成功 / ${failed}失败`);
    } catch (error) {
      console.error('批量加载过程中发生错误:', error);
      message.error('批量加载过程中发生错误');
    }
  };

  /**
   * 更新图表配置数据
   * 🔥 智能更新配置，支持不同图表类型
   */
  const updateChartConfigWithData = async (chartConfig: ChartConfig, data: ChartDataItem[], newDataSource?: string) => {
    try {
      // 根据图表类型和ID选择更新方法
      if (chartConfig.id.includes('sourceOfClues')) {
        // 线索图表使用专用更新方法
        const { updateClueSourceChartData } = await import('../config/clueSourceChartConfig');

        const updateConfig = updateClueSourceChartData(
          chartConfig,
          data as any,
          chartConfig.customProps?.drillDownLevel || 0,
          chartConfig.customProps?.drillDownData,
          chartConfig.customProps?.parentData,
          !data || data.length === 0
        );

        // 如果有新数据源，更新数据源信息
        if (newDataSource) {
          updateConfig.dataSource = newDataSource;
          updateConfig.customProps = {
            ...updateConfig.customProps,
            currentDataSource: newDataSource,
          };
        }

        tabConfigManager.updateChartConfig(chartConfig.id, updateConfig);
      } else {
        // 通用图表更新
        const updateConfig = {
          customProps: {
            ...chartConfig.customProps,
            loading: false,
            isEmpty: !data || data.length === 0,
            lastUpdated: new Date().toISOString(),
          },
          options: {
            ...chartConfig.options,
            // 这里可以根据图表类型更新具体的数据
          },
        };

        if (newDataSource) {
          updateConfig.customProps.currentDataSource = newDataSource;
        }

        tabConfigManager.updateChartConfig(chartConfig.id, updateConfig);
      }

      console.log(`📊 图表配置更新成功: ${chartConfig.id}`);
    } catch (error) {
      console.error('更新图表配置失败:', error);
      // 不抛出错误，避免影响数据加载流程
    }
  };

  /**
   * 获取图表加载状态
   */
  const getLoadingState = (chartId: string): ChartLoadingState => {
    return (
      loadingStates[chartId] || {
        loading: false,
        error: null,
        lastUpdated: null,
        strategy: null,
      }
    );
  };

  /**
   * 获取所有加载状态
   */
  const getAllLoadingStates = () => {
    return { ...loadingStates };
  };

  /**
   * 清理资源
   */
  const cleanup = () => {
    strategyCache.clear();
    Object.keys(loadingStates).forEach((key) => delete loadingStates[key]);
  };

  /**
   * 🔥 新增：二级下探数据加载
   * 专门用于下探场景，自动注入筛选参数
   */
  const loadSecondLevelData = async (chartId: string, parentSourceId: string, customParams?: Partial<commonQueryParams>): Promise<any> => {
    const chartConfig = tabConfigManager.getChartConfig(chartId);
    if (!chartConfig) {
      throw new Error(`图表配置未找到: ${chartId}`);
    }

    const strategy = getStrategy(chartConfig);

    console.log(`🎯 二级下探数据加载 [${strategy.strategyType}]: ${chartId} -> ${parentSourceId}`);

    try {
      // 检查策略是否支持二级下探
      if (strategy.strategyType === 'clue-source' && (strategy as any).loadSecondLevelData) {
        return await (strategy as any).loadSecondLevelData(chartConfig, parentSourceId, customParams);
      } else {
        // 降级方案：使用数据源管理器
        console.warn('策略不支持二级下探，使用降级方案');
        const { useDataSourceManager } = await import('./useDataSourceManager');
        const dataSourceManager = useDataSourceManager();

        // 🔥 仍然通过装饰器注入筛选参数
        const apiDecorator = new (await import('../strategies/ChartDataLoadingStrategy')).ApiCallDecorator();
        return await apiDecorator.decorateApiCall(async (params) => {
          const isValidClue = chartConfig.dataSource?.includes('sourceOfEffectiveClues');
          return await dataSourceManager.fetchAndTransformSecondLevelData(params, parentSourceId, isValidClue);
        }, customParams);
      }
    } catch (error) {
      console.error(`二级下探数据加载失败: ${chartId}`, error);
      throw error;
    }
  };

  return {
    // 核心方法 - 🔥 自动注入筛选参数
    loadChartData,
    refreshChartData,
    switchDataSourceAndReload,
    loadAllAsyncCharts,
    loadSecondLevelData, // 🔥 新增二级下探数据加载

    // 状态管理
    getLoadingState,
    getAllLoadingStates,
    loadingStates,

    // 工具方法
    cleanup,

    // 内部方法暴露（用于调试和扩展）
    getStrategy,
    updateChartConfigWithData,
  };
}
