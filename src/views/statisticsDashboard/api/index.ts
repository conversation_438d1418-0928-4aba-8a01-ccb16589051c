import { defHttpStatistics } from '/@/utils/http/axios';
import { StatisticalDimensionTypeEnum } from '../enums';

/**
 * 看板统计API
 */

export interface commonQueryParams {
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 统计维度 */
  statisticalDimension: StatisticalDimensionTypeEnum;
  /** 城市编码 */
  cityCode?: string;
  /** 国家编码 */
  countryCode?: string;
  /** 经销商编码 */
  dealerCode?: string;
  /** 省份编码 */
  provinceCode?: string;
  /** 大区编码 */
  regionCenterCode?: string; //
  /** 区域编码 */
  regionCode?: string;
}

export interface QueryAllClueSourceParams extends commonQueryParams {}

/**
 * 查询全量线索来源
 */
export const queryAllClueSource = (data: QueryAllClueSourceParams) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueAllOneSource',
    data,
  });
};

/** 全量线索来源返回数据结构 & 有效线索来源返回数据结构 */
export interface AllClueSourceResponseType {
  clueOneSourceResponseList: [
    {
      clueOneSourceDtoList: [
        {
          oneSourceCount: number; // 一级来源数量
          oneSourceId: string; // 一级线索ID -对应 ClueSourceEnum
          oneSourceName: string; // 一级来源名称
          oneSourcePercent: number; // 一级来源占比
        },
      ];
      statisticsDate: string; // 一级来源统计日期
    },
  ];
  clueTwoSourceResponseList: [
    {
      clueTwoSourceDtoList: [
        {
          oneSourceName: string; // 一级来源名称
          twoSourceCount: number; // 二级来源数量
          twoSourceId: string; // 二级线索ID -对应 ClueSourceEnum
          twoSourceName: string; // 二级来源名称
          twoSourcePercent: number; // 二级来源占比
        },
      ];
      statisticsDate: string; // 二级来源统计日期
    },
  ];
  statisticalDimension: StatisticalDimensionTypeEnum; // 统计维度
}

export interface QueryAllClueSourceSecondParams extends commonQueryParams {
  /** 一级来源 ID */
  oneSourceId: string; // 对应 ClueSourceEnum
}

/**
 * 查询全量线索下探到二级来源
 */
export const queryAllClueSourceSecond = (data: Partial<QueryAllClueSourceSecondParams>) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueAllTwoSource',
    data,
  });
};

/**
 * 查询有效线索一级来源
 */
export const queryValidClueSource = (data: QueryAllClueSourceParams) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueValidOneSource',
    data,
  });
};

/**
 * 查询有效线索下探到二级来源
 */
export const queryValidClueSourceSecond = (data: Partial<QueryAllClueSourceSecondParams>) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueValidTwoSource',
    data,
  });
};

interface OverViewCount {
  count: number; // 数量
  ringRatio: number; // 环比
  ringRatioTrend: boolean; // 环比趋势，true 上升 false 下降
  yearRatio: number; // 同比
  yearRatioTrend: boolean; // 同比趋势，true 上升 false 下降
  percent: number; // 百分比
}

export interface ClueOverViewResponse {
  // 全量线索总量
  clueAllCount: OverViewCount;
  // 线索成交量
  clueDealCount: OverViewCount;
  // 线索跟进率
  clueFollowPercent: OverViewCount;
  // 有效线索总量
  clueValidCount: OverViewCount;
  // 线索有效率
  clueValidPercent: OverViewCount;
  // 线索战胜率
  clueWinPercent: OverViewCount;
}

/**
 * 查询线索总览
 */
export const queryClueOverView = (data: Partial<commonQueryParams>) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueOverView',
    data,
  });
};
