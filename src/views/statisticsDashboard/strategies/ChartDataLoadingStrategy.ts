/**
 * 图表数据加载策略系统
 * 采用策略模式 + 装饰器模式 + 工厂模式解决统一筛选参数管理和数据加载扩展性问题
 */

import { reactive } from 'vue';
import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import type { commonQueryParams } from '../api';
import { getCurrentQueryParams } from '../hooks/useQueryParamsContext';
import { message } from 'ant-design-vue';

// ========== 核心接口定义 ==========

/**
 * 图表数据加载策略基础接口
 */
export interface IChartDataLoadingStrategy {
  /** 策略类型标识 */
  readonly strategyType: string;

  /** 支持的图表类型 */
  readonly supportedChartTypes: string[];

  /** 加载图表数据 */
  loadData(chartConfig: ChartConfig, customParams?: Partial<commonQueryParams>): Promise<ChartDataItem[]>;

  /** 刷新图表数据 */
  refreshData(chartConfig: ChartConfig, customParams?: Partial<commonQueryParams>): Promise<ChartDataItem[]>;

  /** 切换数据源 */
  switchDataSource?(chartConfig: ChartConfig, newDataSource: string, customParams?: Partial<commonQueryParams>): Promise<ChartDataItem[]>;

  /** 验证图表配置 */
  validateConfig(chartConfig: ChartConfig): boolean;
}

/**
 * API调用装饰器接口
 */
export interface IApiCallDecorator {
  /** 装饰API调用，自动注入筛选参数 */
  decorateApiCall<T>(apiCall: (params: commonQueryParams) => Promise<T>, customParams?: Partial<commonQueryParams>): Promise<T>;
}

// ========== 装饰器实现：统一筛选参数注入 ==========

/**
 * API调用装饰器 - 自动注入筛选参数
 */
export class ApiCallDecorator implements IApiCallDecorator {
  async decorateApiCall<T>(apiCall: (params: commonQueryParams) => Promise<T>, customParams: Partial<commonQueryParams> = {}): Promise<T> {
    try {
      // 🔥 统一获取筛选参数
      const currentParams = getCurrentQueryParams();

      // 合并参数：自定义参数 > 筛选器参数 > 默认参数
      const finalParams = {
        ...currentParams,
        ...customParams,
      } as commonQueryParams;

      console.log('🚀 API调用装饰器 - 最终参数:', finalParams);

      return await apiCall(finalParams);
    } catch (error) {
      console.error('🚨 API调用装饰器 - 调用失败:', error);
      throw error;
    }
  }
}

// ========== 抽象基类：数据加载策略模板 ==========

/**
 * 抽象基类 - 实现模板方法模式
 */
export abstract class BaseChartDataLoadingStrategy implements IChartDataLoadingStrategy {
  protected apiDecorator: IApiCallDecorator;
  protected loadingStates = reactive<Record<string, boolean>>({});

  constructor() {
    this.apiDecorator = new ApiCallDecorator();
  }

  // 抽象属性和方法 - 子类必须实现
  abstract readonly strategyType: string;
  abstract readonly supportedChartTypes: string[];
  protected abstract fetchApiData(params: commonQueryParams, chartConfig?: ChartConfig): Promise<any>;
  protected abstract transformData(apiData: any): Promise<ChartDataItem[]>;

  /**
   * 模板方法：标准数据加载流程
   */
  async loadData(chartConfig: ChartConfig, customParams: Partial<commonQueryParams> = {}): Promise<ChartDataItem[]> {
    // 前置验证
    if (!this.validateConfig(chartConfig)) {
      throw new Error(`图表配置验证失败: ${chartConfig.id}`);
    }

    // 设置加载状态
    this.setLoadingState(chartConfig.id, true);

    try {
      // 执行数据加载流程
      const result = await this.performDataLoading(chartConfig, customParams);

      // 清除加载状态
      this.setLoadingState(chartConfig.id, false);

      return result;
    } catch (error) {
      // 错误处理
      this.setLoadingState(chartConfig.id, false);
      this.handleLoadingError(error, chartConfig);
      throw error;
    }
  }

  /**
   * 执行数据加载的核心流程
   */
  protected async performDataLoading(chartConfig: ChartConfig, customParams: Partial<commonQueryParams>): Promise<ChartDataItem[]> {
    // 通过装饰器调用API，自动注入筛选参数
    const apiData = await this.apiDecorator.decorateApiCall((params) => this.fetchApiData(params, chartConfig), customParams);

    // 数据转换
    const transformedData = await this.transformData(apiData);

    // 数据验证
    this.validateData(transformedData);

    return transformedData;
  }

  /**
   * 刷新数据（复用加载逻辑）
   */
  async refreshData(chartConfig: ChartConfig, customParams: Partial<commonQueryParams> = {}): Promise<ChartDataItem[]> {
    return this.loadData(chartConfig, customParams);
  }

  /**
   * 默认的数据源切换实现
   */
  async switchDataSource(chartConfig: ChartConfig, newDataSource: string, customParams: Partial<commonQueryParams> = {}): Promise<ChartDataItem[]> {
    // 子类可以重写这个方法
    console.log(`切换数据源: ${chartConfig.id} -> ${newDataSource}`);
    return this.loadData(chartConfig, customParams);
  }

  /**
   * 配置验证
   */
  validateConfig(chartConfig: ChartConfig): boolean {
    return !!(chartConfig.id && this.supportedChartTypes.includes(chartConfig.type));
  }

  /**
   * 数据验证
   */
  protected validateData(data: ChartDataItem[]): void {
    if (!Array.isArray(data)) {
      throw new Error('转换后的数据必须是数组格式');
    }
  }

  /**
   * 错误处理
   */
  protected handleLoadingError(error: any, chartConfig: ChartConfig): void {
    console.error(`图表数据加载失败 [${this.strategyType}]:`, error);
    message.error(`${chartConfig.title}数据加载失败`);
  }

  /**
   * 加载状态管理
   */
  protected setLoadingState(chartId: string, loading: boolean): void {
    this.loadingStates[chartId] = loading;
  }

  public getLoadingState(chartId: string): boolean {
    return this.loadingStates[chartId] || false;
  }
}

// ========== 具体策略实现 ==========

/**
 * 线索来源图表数据加载策略
 */
export class ClueSourceChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'clue-source';
  readonly supportedChartTypes = ['bar', 'column'];

  protected async fetchApiData(params: commonQueryParams, chartConfig?: any): Promise<any> {
    // 动态导入API方法，避免循环依赖
    const { queryAllClueSource, queryValidClueSource } = await import('../api');

    // 🔥 根据图表配置中的数据源选择合适的API
    const dataSource = chartConfig?.dataSource || chartConfig?.customProps?.currentDataSource;

    if (dataSource === 'sourceOfEffectiveClues') {
      console.log('🎯 使用有效线索来源API');
      return await queryValidClueSource(params);
    } else {
      console.log('🎯 使用全量线索来源API');
      return await queryAllClueSource(params);
    }
  }

  protected async transformData(apiData: any): Promise<ChartDataItem[]> {
    // 动态导入数据转换工具
    const { transformApiDataToChartData } = await import('../utils/dataTransform');
    return transformApiDataToChartData(apiData);
  }

  async switchDataSource(chartConfig: ChartConfig, newDataSource: string, customParams: Partial<commonQueryParams> = {}): Promise<ChartDataItem[]> {
    // 通过图表配置传递数据源信息
    const configWithNewDataSource = {
      ...chartConfig,
      dataSource: newDataSource,
      customProps: {
        ...chartConfig.customProps,
        currentDataSource: newDataSource,
      },
    };

    return this.loadData(configWithNewDataSource, customParams);
  }
}

/**
 * 默认图表数据加载策略（Mock数据）
 */
export class DefaultMockChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'default-mock';
  readonly supportedChartTypes = ['bar', 'line', 'pie', 'column', 'area'];

  protected async fetchApiData(_params: commonQueryParams, _chartConfig?: ChartConfig): Promise<any> {
    // 返回模拟数据，保持原有Mock行为
    console.log('🔄 使用Mock数据策略');
    return [];
  }

  protected async transformData(apiData: any): Promise<ChartDataItem[]> {
    // Mock数据直接返回
    return apiData || [];
  }
}

// ========== 工厂模式：策略创建和管理 ==========

/**
 * 图表数据加载策略工厂
 */
export class ChartDataLoadingStrategyFactory {
  private static strategies: Map<string, IChartDataLoadingStrategy> = new Map();

  /**
   * 注册策略
   */
  static registerStrategy(strategy: IChartDataLoadingStrategy): void {
    this.strategies.set(strategy.strategyType, strategy);
    console.log(`📝 注册数据加载策略: ${strategy.strategyType}`);
  }

  /**
   * 根据图表配置创建合适的策略
   */
  static createStrategy(chartConfig: ChartConfig): IChartDataLoadingStrategy {
    // 1. 优先根据自定义配置选择策略
    if (chartConfig.customProps?.loadingStrategy) {
      const strategy = this.strategies.get(chartConfig.customProps.loadingStrategy);
      if (strategy) {
        return strategy;
      }
    }

    // 2. 根据图表ID和类型匹配策略
    const matchingStrategy = Array.from(this.strategies.values()).find((strategy) => {
      // 线索相关图表使用线索策略
      if (chartConfig.id.includes('sourceOfClues') || chartConfig.id.includes('clue')) {
        return strategy.strategyType === 'clue-source';
      }

      // 需要异步数据的图表，尝试匹配专用策略
      if (chartConfig.customProps?.needsAsyncData) {
        return strategy.supportedChartTypes.includes(chartConfig.type) && strategy.strategyType !== 'default-mock';
      }

      return false;
    });

    if (matchingStrategy) {
      return matchingStrategy;
    }

    // 3. 默认使用Mock策略
    const defaultStrategy = this.strategies.get('default-mock');
    if (!defaultStrategy) {
      throw new Error('默认Mock策略未注册');
    }

    return defaultStrategy;
  }

  /**
   * 获取所有注册的策略
   */
  static getAllStrategies(): IChartDataLoadingStrategy[] {
    return Array.from(this.strategies.values());
  }

  /**
   * 初始化默认策略
   */
  static initializeDefaultStrategies(): void {
    // 注册内置策略
    this.registerStrategy(new ClueSourceChartStrategy());
    this.registerStrategy(new DefaultMockChartStrategy());

    console.log('🚀 默认策略初始化完成');
  }
}

// 自动初始化默认策略
ChartDataLoadingStrategyFactory.initializeDefaultStrategies();
