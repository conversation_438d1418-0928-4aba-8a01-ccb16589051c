/**
 * 线索来源图表配置管理器
 * 统一管理线索来源相关的图表配置，从mock数据层分离出来
 */

import { useI18n } from '/@/hooks/web/useI18n';
import type { ChannelConfig, ChartConfig, ChartDataItem, MonthlyChannelData, DrillDownConfig } from '../types/statisticDashboard';
import { calculatePercentage, createTooltipFormatter, formatValueWithPercentage, generateChartDataItem } from '../utils';

const { t } = useI18n('common');

/**
 * 线索来源下探配置 - 新版本使用策略系统
 */
export const clueSourceDrillConfig: DrillDownConfig = {
  enabled: true,
  currentLevel: 0,
  maxLevel: 1,
  dataStrategy: 'async',
  levels: [
    {
      level: 0,
      dataKey: 'channelKey',
      titleField: 'name',
      valueField: 'value',
      colorField: 'channelKey',
    },
    {
      level: 1,
      dataKey: 'subChannelKey',
      titleField: 'name',
      valueField: 'value',
      colorField: 'subChannelKey',
      parentKey: 'channelKey',
    },
  ],
  // 🔥 新策略模式：不需要 dataProvider，直接在 useChartActions 中使用策略系统
};

/**
 * 线索来源渠道配置
 */
export const clueSourceChannelConfig: ChannelConfig[] = [
  {
    key: 'onlinePublic',
    name: t('onlinePublicDomain'),
    color: '#5470c6', // 蓝色
    children: [
      { key: 'social', name: '社媒', color: '#9c27b0' },
      { key: 'vertical', name: '垂媒', color: '#607d8b' },
    ],
  },
  {
    key: 'onlinePrivate',
    name: t('onlinePrivateDomain'),
    color: '#91cc75', // 绿色
    children: [
      { key: 'app', name: 'APP', color: '#9c27b0' },
      { key: 'website', name: t('officeWeb'), color: '#e91e63' },
      { key: 'serviceCenter', name: t('customerServiceCenter'), color: '#00bcd4' },
    ],
  },
  {
    key: 'offlinePrivate',
    name: t('offlinePrivateDomain'),
    color: '#fac858', // 黄色
    children: [
      { key: 'marketing', name: '营销活动', color: '#ff9800' },
      { key: 'carshow', name: '车展', color: '#795548' },
    ],
  },
];

/**
 * 通用tooltip格式化函数（兼容现有用法）
 */
export const formatTooltip = createTooltipFormatter({
  showPercentage: true,
});

/**
 * 通用的渠道配置查找函数
 */

export const findChannelConfig = (channelKey: string): ChannelConfig | null => {
  for (const config of clueSourceChannelConfig) {
    if (config.key === channelKey) {
      return config;
    }
    if (config.children) {
      for (const child of config.children) {
        if (child.key === channelKey) {
          return child;
        }
      }
    }
  }
  return null;
};

/**
 * 线索来源图表配置管理器
 */
export class ClueSourceChartConfigManager {
  private static instance: ClueSourceChartConfigManager;

  static getInstance(): ClueSourceChartConfigManager {
    if (!ClueSourceChartConfigManager.instance) {
      ClueSourceChartConfigManager.instance = new ClueSourceChartConfigManager();
    }
    return ClueSourceChartConfigManager.instance;
  }

  /**
   * 生成基础图表配置（不包含数据）
   */
  generateBaseConfig(dataSource: 'sourceOfAllClues' | 'sourceOfEffectiveClues' = 'sourceOfAllClues', drillDownLevel: number = 0): ChartConfig {
    const isAllClues = dataSource === 'sourceOfAllClues';
    // 根据下探层级决定标题
    let title: string;
    if (drillDownLevel === 1) {
      title = t('channelLeadAnalysis');
    } else {
      title = isAllClues ? t('sourceOfAllClues') : t('effectiveSourcesOfClues');
    }

    return {
      id: 'sourceOfClues',
      type: 'bar',
      title,
      dataSource,
      drillDown: clueSourceDrillConfig,
      customProps: {
        switchable: true,
        currentDataSource: dataSource,
        alternativeDataSource: isAllClues ? 'sourceOfEffectiveClues' : 'sourceOfAllClues',
        alternativeTitle: isAllClues ? t('effectiveSourcesOfClues') : t('sourceOfAllClues'),
        drillDownLevel,
        needsAsyncData: true,
        loading: true,
      },
      options: {
        title: { show: false },
        color: clueSourceChannelConfig.map((config) => config.color),
        grid: {
          left: '4%',
          right: '4%',
          bottom: '15%',
          containLabel: true,
        },
        legend: {
          data: clueSourceChannelConfig.map((config) => config.name),
          bottom: '5%',
          left: 'center',
        },
        xAxis: {
          type: 'category',
          data: [], // 空数据，等待异步更新
          axisLabel: {
            interval: 0,
            rotate: 30,
          },
        },
        yAxis: {
          type: 'value',
          name: t('numberOfClues'),
        },
        series: clueSourceChannelConfig.map((channelConfig) => ({
          name: channelConfig.name,
          type: 'bar',
          data: [], // 空数据，等待异步更新
          itemStyle: {
            color: channelConfig.color,
          },
          label: {
            show: true,
            position: 'top',
            formatter: (params: any) => {
              return formatValueWithPercentage(params.data.value, params.data.percent);
            },
          },
        })),
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: (params: any) => {
            return formatTooltip(params);
          },
        },
      },
      size: { height: 450 },
      position: { x: 0, y: 0 },
    };
  }

  /**
   * 更新图表配置的数据部分
   */
  updateChartData(
    baseConfig: ChartConfig,
    data: MonthlyChannelData[],
    drillDownLevel: number = 0,
    drillDownData?: ChartDataItem[],
    parentData?: ChartDataItem
  ): Partial<ChartConfig> {
    const isSecondLevel = drillDownLevel === 1;

    if (isSecondLevel) {
      return this.generateSecondLevelUpdate(baseConfig, drillDownData, parentData);
    } else {
      return this.generateFirstLevelUpdate(baseConfig, data);
    }
  }

  /**
   * 生成一级数据更新配置
   */
  private generateFirstLevelUpdate(baseConfig: ChartConfig, data: MonthlyChannelData[]): Partial<ChartConfig> {
    const series = clueSourceChannelConfig.map((channelConfig) => ({
      name: channelConfig.name,
      type: 'bar',
      data: data?.map((item) => {
        const value = item.channels[channelConfig.key] || 0;
        const percent = item.value > 0 ? calculatePercentage(value, item.value) : '0.0';
        return generateChartDataItem(item.name, value, percent, {
          channelKey: channelConfig.key,
        });
      }),
      itemStyle: {
        color: channelConfig.color,
      },
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => {
          return formatValueWithPercentage(params.data.value, params.data.percent);
        },
      },
    }));

    return {
      customProps: {
        ...baseConfig.customProps,
        loading: false,
        drillDownLevel: 0,
      },
      options: {
        ...baseConfig.options,
        xAxis: {
          ...baseConfig.options?.xAxis,
          data: data?.map((item) => item.name) ?? [],
        },
        series: series as any,
      },
    };
  }

  /**
   * 生成二级数据更新配置
   */
  private generateSecondLevelUpdate(baseConfig: ChartConfig, drillDownData?: ChartDataItem[], parentData?: ChartDataItem): Partial<ChartConfig> {
    const actualDrillDownData = drillDownData || [];

    if (!parentData) {
      return {
        title: t('channelLeadAnalysis'),
        customProps: {
          ...baseConfig.customProps,
          loading: false,
          drillDownLevel: 1,
        },
      };
    }

    // 获取父级渠道配置
    const parentChannelKey = parentData.channelKey;
    const parentConfig = findChannelConfig(parentChannelKey);

    if (!parentConfig?.children) {
      return {
        customProps: {
          ...baseConfig.customProps,
          loading: false,
          drillDownLevel: 1,
        },
      };
    }

    // 按子渠道分组数据
    const groupedData: Record<string, ChartDataItem[]> = {};
    parentConfig.children.forEach((childConfig) => {
      groupedData[childConfig.key] = actualDrillDownData.filter((item) => item.channelKey === childConfig.key);
    });

    // 获取所有月份
    const months = Array.from(new Set(actualDrillDownData.map((item) => item.month))).sort();

    const series = parentConfig.children.map((childConfig) => ({
      name: childConfig.name,
      type: 'bar',
      data:
        groupedData[childConfig.key]?.map((item) => {
          const monthlyTotal = actualDrillDownData
            .filter((dataItem) => dataItem.month === item.month)
            .reduce((sum, dataItem) => sum + (Number(dataItem.value) || 0), 0);

          const itemValue = Number(item.value) || 0;
          const percent = monthlyTotal > 0 ? calculatePercentage(itemValue, monthlyTotal) : '0.0';
          return generateChartDataItem(item.month, itemValue, percent, {
            channelKey: childConfig.key,
          });
        }) || [],
      itemStyle: {
        color: childConfig.color,
      },
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => {
          return formatValueWithPercentage(params.data.value, params.data.percent);
        },
      },
    }));

    return {
      title: t('channelLeadAnalysis'),
      customProps: {
        ...baseConfig.customProps,
        loading: false,
        drillDownLevel: 1,
      },
      options: {
        ...baseConfig.options,
        color: parentConfig.children.map((child) => child.color),
        legend: {
          data: parentConfig.children.map((child) => child.name),
          bottom: '5%',
        },
        xAxis: {
          ...baseConfig.options?.xAxis,
          data: months,
        },
        series: series as any,
      },
    };
  }

  /**
   * 获取渠道配置
   */
  getChannelConfig(): ChannelConfig[] {
    return clueSourceChannelConfig;
  }

  /**
   * 根据数据源获取标题
   */
  getTitleByDataSource(dataSource: string, drillDownLevel: number = 0): string {
    if (drillDownLevel === 1) {
      return t('channelLeadAnalysis');
    }

    const isAllClues = dataSource === 'sourceOfAllClues';
    return isAllClues ? t('sourceOfAllClues') : t('effectiveSourcesOfClues');
  }

  /**
   * 获取替代数据源信息
   */
  getAlternativeInfo(currentDataSource: string): { dataSource: string; title: string } {
    const isAllClues = currentDataSource === 'sourceOfAllClues';
    return {
      dataSource: isAllClues ? 'sourceOfEffectiveClues' : 'sourceOfAllClues',
      title: isAllClues ? t('effectiveSourcesOfClues') : t('sourceOfAllClues'),
    };
  }
}

/**
 * 获取线索来源图表配置管理器实例
 */
export function getClueSourceChartConfigManager(): ClueSourceChartConfigManager {
  return ClueSourceChartConfigManager.getInstance();
}

/**
 * 便捷函数：生成基础配置
 */
export function generateBaseClueSourceChartConfig(
  dataSource: 'sourceOfAllClues' | 'sourceOfEffectiveClues' = 'sourceOfAllClues',
  drillDownLevel: number = 0
): ChartConfig {
  return getClueSourceChartConfigManager().generateBaseConfig(dataSource, drillDownLevel);
}

/**
 * 便捷函数：更新图表数据
 */
export function updateClueSourceChartData(
  baseConfig: ChartConfig,
  data: MonthlyChannelData[],
  drillDownLevel: number = 0,
  drillDownData?: ChartDataItem[],
  parentData?: ChartDataItem,
  isEmpty: boolean = false // 🔥 新增空数据标记参数
): Partial<ChartConfig> {
  const result = getClueSourceChartConfigManager().updateChartData(baseConfig, data, drillDownLevel, drillDownData, parentData);

  // 🔥 在配置中设置空数据标记
  return {
    ...result,
    customProps: {
      ...result.customProps,
      isEmpty: isEmpty, // 设置空数据标记
    },
  };
}

/** 线索来源基础配置-空数据（等待API返回） */
export const sourceOfCluesChartConfigs: ChartConfig[] = [generateBaseClueSourceChartConfig('sourceOfAllClues')];
