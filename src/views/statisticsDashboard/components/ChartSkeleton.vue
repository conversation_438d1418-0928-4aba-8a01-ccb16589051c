<template>
  <div class="h-full flex flex-col items-center justify-center bg-white">
    <div class="flex flex-col items-center gap-4">
      <a-spin size="large" />
      <div class="text-gray-600 text-sm">{{ getLoadingText() }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
  interface Props {
    /** 图表类型 */
    type?: 'bar' | 'pie' | 'line' | 'generic';
    /** 高度 */
    height?: string;
    /** 宽度 */
    width?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    type: 'generic',
    height: '400px',
    width: '100%',
  });

  // 根据图表类型获取加载文字
  const getLoadingText = (): string => {
    const textMap: Record<string, string> = {
      bar: '正在加载柱状图数据...',
      pie: '正在加载饼图数据...',
      line: '正在加载折线图数据...',
      generic: '正在加载图表数据...',
    };
    return textMap[props.type] || textMap.generic;
  };
</script>
