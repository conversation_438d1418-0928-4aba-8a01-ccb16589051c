<template>
  <div class="mb-4">
    <a-card :bordered="false" size="small">
      <template #title>
        <div class="flex items-center gap-2 font-medium">
          <Icon icon="ant-design:filter-outlined" />
          <span>筛选条件</span>
          <!-- <a-badge :count="filterCount" :offset="[10, 0]" /> -->
        </div>
      </template>
      <div class="filter-content">
        <BasicForm @register="registerForm" @field-value-change="handleFieldChange" @submit="handleSubmit" @reset="handleReset" />
      </div>
    </a-card>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'FilterPanel',
  };
</script>
<script setup lang="ts">
  import { Icon } from '/@/components/Icon';
  import { useFilters } from '../hooks/useFilters';
  import { message } from 'ant-design-vue';
  import { BasicForm } from '/@/components/Form';

  // Props定义
  interface Props {
    /** 是否自动应用筛选 */
    autoApply?: boolean;
    /** 是否显示快捷筛选 */
    showPresets?: boolean;
    /** 是否启用用户数据权限, 开启后 会把筛选条件里的 大区 和 国家进行赋值当前登陆的用户，不允许修改 */
    enableUserDataAuth?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    autoApply: false,
    showPresets: true,
    enableUserDataAuth: true,
  });

  // Emits定义
  interface Emits {
    'filter-change': [filters: any];
    'filter-apply': [filters: any];
    'filter-clear': [];
  }

  const emit = defineEmits<Emits>();

  // 使用筛选器Hook
  const {
    registerForm,
    formMethods,
    getPeriodRangeDefaultValue,
    getPickerConfig,
    handleFieldChange,
    getCurrentFilters,
    transformFiltersToQueryParams,
    getDefaultQueryParams,
  } = useFilters({
    ...props,
  });

  const { updateSchema, setFieldsValue } = formMethods;

  /**
   * 获取当前筛选条件
   */
  const getFilters = () => {
    return getCurrentFilters();
  };

  /**
   * 获取API查询参数
   */
  const getQueryParams = () => {
    return transformFiltersToQueryParams();
  };

  /**
   * 获取默认查询参数
   */
  const getDefaultParams = () => {
    return getDefaultQueryParams();
  };

  // 处理表单提交（应用筛选）
  const handleSubmit = async (values: Record<string, any>) => {
    if (Reflect.ownKeys(values).length === 0) return;
    console.log('表单提交数据:', values);
    emit('filter-apply', values);
  };

  // 处理表单重置（清除筛选）
  const handleReset = () => {
    // 重置为默认值
    const defaultRange = getPeriodRangeDefaultValue('day');

    // 清除表单值
    setFieldsValue({
      statPeriod: 'day',
      periodRange: defaultRange,
      regions: [],
    });

    // 重置periodRange的配置为day模式
    const pickerConfig = getPickerConfig('day');
    updateSchema({
      field: 'periodRange',
      componentProps: {
        picker: pickerConfig.picker,
        format: pickerConfig.format, // 显示格式
        valueFormat: pickerConfig.valueFormat, // 提交格式（统一格式）
        valueType: 'array', // 保持数组格式
        style: { width: '100%' },
      },
    });

    emit('filter-clear');
    message.success('筛选条件已清除');
  };

  // 暴露方法给父组件
  defineExpose({
    getFilters,
    getQueryParams,
    getDefaultParams,
  });
</script>
