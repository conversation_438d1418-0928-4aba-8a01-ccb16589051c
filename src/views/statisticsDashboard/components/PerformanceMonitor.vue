<template>
  <div class="performance-monitor">
    <a-dropdown :trigger="['click']" placement="bottomRight">
      <a-button type="text" size="small" class="monitor-button">
        <Icon icon="ant-design:dashboard-outlined" />
        <span class="ml-1">性能</span>
        <a-badge :color="performanceLevel.color" :text="performanceScore.toString()" class="ml-1" />
      </a-button>

      <template #overlay>
        <a-card class="performance-panel" size="small" :bordered="false">
          <template #title>
            <div class="flex items-center gap-2">
              <Icon icon="ant-design:dashboard-outlined" />
              <span>性能监控</span>
              <a-tag :color="performanceLevel.color" size="small">
                {{ performanceLevel.text }}
              </a-tag>
            </div>
          </template>

          <template #extra>
            <a-button type="text" size="small" @click="resetMetrics">
              <Icon icon="ant-design:reload-outlined" />
            </a-button>
          </template>

          <!-- 性能指标 -->
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-label">渲染时间</div>
              <div class="metric-value"> {{ metrics.renderTime.toFixed(1) }}ms </div>
              <div class="metric-status" :class="getRenderTimeStatus()">
                {{ getRenderTimeText() }}
              </div>
            </div>

            <div class="metric-item">
              <div class="metric-label">帧率</div>
              <div class="metric-value"> {{ metrics.fps }}fps </div>
              <div class="metric-status" :class="getFpsStatus()">
                {{ getFpsText() }}
              </div>
            </div>

            <div class="metric-item">
              <div class="metric-label">内存使用</div>
              <div class="metric-value"> {{ metrics.memoryUsage }}MB </div>
              <div class="metric-status" :class="getMemoryStatus()">
                {{ getMemoryText() }}
              </div>
            </div>

            <div class="metric-item">
              <div class="metric-label">错误次数</div>
              <div class="metric-value">
                {{ metrics.errorCount }}
              </div>
              <div class="metric-status" :class="getErrorStatus()">
                {{ getErrorText() }}
              </div>
            </div>
          </div>

          <!-- 性能建议 -->
          <div v-if="suggestions.length > 0" class="suggestions">
            <a-divider orientation="left" plain>
              <span class="text-xs">优化建议</span>
            </a-divider>
            <div class="suggestion-list">
              <div v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item">
                <Icon icon="ant-design:bulb-outlined" class="text-yellow-500" />
                <span class="text-xs">{{ suggestion }}</span>
              </div>
            </div>
          </div>

          <!-- 性能历史图表 -->
          <div v-if="showHistory" class="history-chart">
            <a-divider orientation="left" plain>
              <span class="text-xs">性能趋势</span>
            </a-divider>
            <div class="chart-container">
              <!-- 这里可以集成一个小的性能趋势图 -->
              <div class="trend-line">
                <div
                  v-for="(point, index) in performanceHistory.slice(-20)"
                  :key="index"
                  class="trend-point"
                  :style="{
                    height: `${(point.renderTime / 50) * 100}%`,
                    backgroundColor: point.renderTime > 16 ? '#ff4d4f' : '#52c41a',
                  }"
                ></div>
              </div>
            </div>
          </div>
        </a-card>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useChartPerformance } from '../hooks/useChartPerformance';

  interface Props {
    /** 是否显示历史趋势 */
    showHistory?: boolean;
  }

  withDefaults(defineProps<Props>(), {
    showHistory: true,
  });

  // 使用性能监控Hook
  const { metrics, performanceScore, performanceLevel, performanceHistory, getPerformanceSuggestions, resetMetrics } = useChartPerformance();

  // 性能建议
  const suggestions = computed(() => getPerformanceSuggestions());

  // 渲染时间状态
  const getRenderTimeStatus = () => {
    if (metrics.renderTime <= 16) return 'status-good';
    if (metrics.renderTime <= 33) return 'status-warning';
    return 'status-error';
  };

  const getRenderTimeText = () => {
    if (metrics.renderTime <= 16) return '优秀';
    if (metrics.renderTime <= 33) return '一般';
    return '较差';
  };

  // FPS状态
  const getFpsStatus = () => {
    if (metrics.fps >= 30) return 'status-good';
    if (metrics.fps >= 20) return 'status-warning';
    return 'status-error';
  };

  const getFpsText = () => {
    if (metrics.fps >= 30) return '流畅';
    if (metrics.fps >= 20) return '一般';
    return '卡顿';
  };

  // 内存状态
  const getMemoryStatus = () => {
    if (metrics.memoryUsage <= 50) return 'status-good';
    if (metrics.memoryUsage <= 100) return 'status-warning';
    return 'status-error';
  };

  const getMemoryText = () => {
    if (metrics.memoryUsage <= 50) return '正常';
    if (metrics.memoryUsage <= 100) return '偏高';
    return '过高';
  };

  // 错误状态
  const getErrorStatus = () => {
    if (metrics.errorCount === 0) return 'status-good';
    if (metrics.errorCount <= 3) return 'status-warning';
    return 'status-error';
  };

  const getErrorText = () => {
    if (metrics.errorCount === 0) return '正常';
    if (metrics.errorCount <= 3) return '少量';
    return '较多';
  };
</script>

<style scoped lang="less">
  .performance-monitor {
    .monitor-button {
      display: flex;
      align-items: center;
      color: #666;
      transition: color 0.3s;
    }

    .monitor-button:hover {
      color: #1890ff;
    }
  }

  .performance-panel {
    width: 320px;
    max-height: 500px;
    overflow-y: auto;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
  }

  .metric-item {
    padding: 8px;
    background: #fafafa;
    border-radius: 4px;
    text-align: center;
  }

  .metric-label {
    font-size: 11px;
    color: #666;
    margin-bottom: 4px;
  }

  .metric-value {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 2px;
  }

  .metric-status {
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
  }

  .status-good {
    background: #f6ffed;
    color: #52c41a;
  }

  .status-warning {
    background: #fffbe6;
    color: #faad14;
  }

  .status-error {
    background: #fff2f0;
    color: #ff4d4f;
  }

  .suggestions {
    margin-top: 16px;
  }

  .suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .suggestion-item {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    padding: 6px;
    background: #fffbe6;
    border-radius: 4px;
    border-left: 3px solid #faad14;
  }

  .history-chart {
    margin-top: 16px;
  }

  .chart-container {
    height: 60px;
    background: #fafafa;
    border-radius: 4px;
    padding: 8px;
  }

  .trend-line {
    display: flex;
    align-items: flex-end;
    height: 100%;
    gap: 1px;
  }

  .trend-point {
    flex: 1;
    min-height: 2px;
    border-radius: 1px;
    transition: all 0.3s ease;
  }
</style>
