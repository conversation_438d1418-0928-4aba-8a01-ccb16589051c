<template>
  <div
    ref="targetRef"
    class="lazy-chart-container"
    :class="{
      'is-loading': state.isLoading,
      'has-error': state.error,
      'is-loaded': state.isLoaded,
    }"
    :style="{ minHeight: props.minHeight }"
  >
    <!-- 加载状态 -->
    <div v-if="!state.isLoaded && !state.error" class="chart-placeholder" :style="{ minHeight: props.minHeight }">
      <div v-if="state.isLoading" class="loading-content">
        <a-spin size="large" />
        <div class="loading-text">正在加载图表...</div>
      </div>
      <div v-else class="waiting-content">
        <div class="placeholder-icon">
          <Icon icon="ant-design:bar-chart-outlined" />
        </div>
        <div class="placeholder-text">图表即将加载</div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="state.error" class="error-content" :style="{ minHeight: props.minHeight }">
      <div class="error-icon">
        <Icon icon="ant-design:exclamation-circle-outlined" />
      </div>
      <div class="error-text">{{ state.error }}</div>
      <a-button type="primary" size="small" @click="handleReload">
        <Icon icon="ant-design:reload-outlined" />
        重新加载
      </a-button>
    </div>

    <!-- 图表内容 -->
    <div v-else-if="state.isLoaded" class="chart-content">
      <slot :isLoaded="state.isLoaded" :reload="handleReload"></slot>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'LazyChartContainer',
  };
</script>
<script setup lang="ts">
  import { watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useChartLazyLoad } from '../hooks/useChartLazyLoad';

  interface Props {
    /** 图表ID */
    chartId: string;
    /** 图表标题 */
    chartTitle?: string;
    /** 懒加载配置 */
    lazyOptions?: {
      rootMargin?: string;
      threshold?: number | number[];
      once?: boolean;
      delay?: number;
    };
    /** 最小高度 */
    minHeight?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    lazyOptions: () => ({
      rootMargin: '100px',
      threshold: 0.1,
      once: true,
      delay: 200,
    }),
    minHeight: '300px',
  });

  // 使用懒加载Hook
  const { targetRef, state, reload } = useChartLazyLoad(props.lazyOptions);

  // 处理重新加载
  const handleReload = () => {
    reload();
  };

  // 监听加载完成状态，记录简化日志
  watch(
    () => state.isLoaded,
    (newVal) => {
      if (newVal) {
        // console.log(`📊 [懒加载] ${props.chartTitle || props.chartId} 加载完成`);
      }
    }
  );
</script>

<style scoped lang="less">
  .lazy-chart-container {
    position: relative;
    width: 100%;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .chart-placeholder,
  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 300px;
    padding: 40px 20px;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .loading-text {
    color: #666;
    font-size: 14px;
  }

  .waiting-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    opacity: 0.6;
  }

  .placeholder-icon {
    font-size: 48px;
    color: #d9d9d9;
  }

  .placeholder-text {
    color: #999;
    font-size: 14px;
  }

  .error-content {
    gap: 16px;
  }

  .error-icon {
    font-size: 48px;
    color: #ff4d4f;
  }

  .error-text {
    color: #ff4d4f;
    font-size: 14px;
    text-align: center;
  }

  .chart-content {
    width: 100%;
    height: 100%;
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
  }

  .is-loading {
    .chart-placeholder {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  /* 错误状态样式 */
  .has-error {
    border: 1px solid #ffccc7;
    background: #fff2f0;
  }

  /* 已加载状态样式 */
  .is-loaded {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .is-loaded:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
</style>
