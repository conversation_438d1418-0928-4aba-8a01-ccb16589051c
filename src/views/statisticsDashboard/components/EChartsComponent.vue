<template>
  <div class="chart-widget" :class="{ 'is-dragging': isDragging }">
    <!-- 拖拽手柄 -->
    <div class="drag-handle" v-if="draggable">
      <Icon icon="ant-design:drag-outlined" />
      <span class="chart-title">{{ config.title }}</span>
    </div>

    <!-- 图表容器 -->
    <div class="chart-container" ref="chartRef" :style="{ height, width }">
      <div ref="echartsRef" class="echarts-wrapper"></div>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'EChartsComponent',
  };
</script>
<script setup lang="ts">
  import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
  import * as echarts from 'echarts';
  import type { ECharts } from 'echarts';
  import { Icon } from '/@/components/Icon';
  import type { ChartConfig, ChartDataItem, ChartEventParams } from '../types/statisticDashboard';
  import { useChartConfig } from '../hooks/useChartConfig';
  import { useChartPerformance } from '../hooks/useChartPerformance';
  import { useChartEventsOptional, useChartActionsOptional } from '../hooks/useChartActions';

  // Props定义
  interface Props {
    /** 图表配置 */
    config: ChartConfig;
    /** 图表数据 */
    data: ChartDataItem[];
    /** 图表高度 */
    height?: string;
    /** 图表宽度 */
    width?: string;
    /** 是否可拖拽 */
    draggable?: boolean;
    /** 是否处于拖拽状态 */
    isDragging?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    height: '400px',
    width: '100%',
    draggable: false,
    isDragging: false,
  });

  // 注意：EChartsComponent 现在完全使用上下文系统，不再需要 emit 事件

  // 使用图表上下文系统
  const chartEvents = useChartEventsOptional();
  const chartActions = useChartActionsOptional();

  // 使用图表配置Hook
  const { generateChartOption: generateConfigOption } = useChartConfig();

  // 使用性能监控Hook
  const {
    recordRenderTime,
    recordDataProcessTime,
    recordError,
    optimizeData,
    createDebouncedRender,
    createThrottledResize,
    // metrics,
    // performanceScore,
    // performanceLevel,
  } = useChartPerformance();

  // 移除了响应式设计Hook的使用

  // 响应式数据
  const chartRef = ref<HTMLElement>();
  const echartsRef = ref<HTMLElement>();
  let chartInstance: ECharts | null = null;

  // 用于跟踪是否点击了数据元素
  let isDataElementClicked = false;

  // 下探状态
  const isDrilling = ref(false);

  // 获取下探状态 - 完全通过上下文系统管理
  const getCurrentLevel = () => {
    return props.config.drillDown?.currentLevel || 0;
  };

  // 计算当前显示的数据（包含性能优化）
  const currentData = computed(() => {
    const startTime = performance.now();

    // 直接使用props.data，数据管理由上下文系统处理
    const data = props.data;

    // 应用数据优化
    const optimizedData = optimizeData(data as { value: number; name: string }[]);

    const endTime = performance.now();
    recordDataProcessTime(startTime, endTime);

    return optimizedData;
  });

  // 初始化图表
  const initChart = async () => {
    if (!echartsRef.value) return;

    try {
      chartInstance = echarts.init(echartsRef.value);

      // 渲染图表
      renderChart();

      // 绑定事件
      bindEvents();
    } catch (error) {
      console.error('初始化图表失败:', error);
    }
  };

  // 渲染图表（原始方法）
  const renderChartInternal = () => {
    if (!chartInstance) return;

    try {
      const startTime = performance.now();

      // 🔥 修复：正确调用generateChartOption方法，传递必要的参数
      const option = generateConfigOption(props.config, currentData.value as ChartDataItem[]);

      // 为下探操作添加特殊动画配置
      if (isDrilling.value) {
        option.animation = true;
        option.animationDuration = 500;
        option.animationEasing = 'cubicOut';
        option.animationDelay = 0;
      }

      chartInstance.setOption(option, true);

      const endTime = performance.now();
      recordRenderTime(startTime, endTime);
    } catch (error) {
      recordError(error as Error);
      throw error;
    }
  };

  // 防抖渲染图表
  const renderChart = createDebouncedRender(renderChartInternal);

  // 绑定事件
  const bindEvents = () => {
    if (!chartInstance) return;

    // 绑定图表点击事件
    chartInstance.on('click', handleChartClick);
    chartInstance.on('dblclick', handleChartDblClick);

    // 统一的空白区域点击处理 - 更优雅的方案
    chartInstance.getZr().on('click', handleZrClick);
  };

  // 处理图表点击事件
  const handleChartClick = (params: any) => {
    // 标记点击了数据元素
    isDataElementClicked = true;

    const eventParams: ChartEventParams = {
      type: 'click',
      data: params.data,
      seriesType: params.seriesType,
      seriesIndex: params.seriesIndex,
      dataIndex: params.dataIndex,
    };

    // 直接使用上下文系统处理图表点击事件
    chartEvents.onChartClick(eventParams);

    // 如果启用了数据下探，直接调用上下文系统的下探处理
    if (props.config.drillDown?.enabled && params.data) {
      chartEvents.onDrillDown(params.data, getCurrentLevel() + 1, props.config);
    }
  };

  // 处理图表双击事件
  const handleChartDblClick = (params: any) => {
    const eventParams: ChartEventParams = {
      type: 'dblclick',
      data: params.data,
      seriesType: params.seriesType,
      seriesIndex: params.seriesIndex,
      dataIndex: params.dataIndex,
    };

    // 直接使用上下文系统处理双击事件
    chartEvents.onChartDblClick(eventParams);
  };

  // 处理ZRender层点击事件（官方推荐的空白区域检测）
  const handleZrClick = (event: any) => {
    // 使用setTimeout确保在数据元素点击事件之后执行
    setTimeout(() => {
      // 如果刚才点击了数据元素，则不处理空白区域点击
      if (isDataElementClicked) {
        isDataElementClicked = false; // 重置标记
        return;
      }

      // 🎯 官方推荐方法：没有target表示点击了空白区域
      if (!event.target) {
        // 点击了真正的空白区域，回到顶层
        if (getCurrentLevel() > 0) {
          resetToTopLevel();
        }

        // 直接使用上下文系统处理区域点击事件
        chartEvents.onChartAreaClick(event);
      }
    }, 0);
  };

  // 移除复杂的检测函数，改用事件监听机制

  // 注意：下探逻辑已移至上下文系统统一处理
  // EChartsComponent 现在只负责触发下探事件，具体处理由上层组件通过上下文系统完成

  // 重置到顶层数据 - 直接使用上下文系统
  const resetToTopLevel = async () => {
    if (chartActions.resetChartToTopLevel) {
      await chartActions.resetChartToTopLevel(props.config);
    } else {
      console.warn('重置功能不可用：未找到resetChartToTopLevel函数');
    }
  };

  // 监听数据变化
  watch(
    () => props.data,
    () => {
      if (chartInstance) {
        renderChart();
      }
    },
    { deep: true }
  );

  // 监听配置变化
  watch(
    () => props.config,
    () => {
      if (chartInstance) {
        renderChart();
      }
    },
    { deep: true }
  );

  // 处理窗口大小变化（原始方法）
  const handleResizeInternal = () => {
    if (!chartInstance) return;

    // 使用带动画的resize方法调整尺寸
    chartInstance.resize({
      animation: {
        duration: 500,
        easing: 'cubicOut',
      },
    });
  };

  // 节流窗口大小变化处理
  const handleResize = createThrottledResize(handleResizeInternal);

  // 组件挂载
  onMounted(() => {
    nextTick(() => {
      initChart();
    });

    window.addEventListener('resize', handleResize);
  });

  // 组件卸载
  onUnmounted(() => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }

    window.removeEventListener('resize', handleResize);
  });

  // 完全移除 defineExpose，实现真正的零父子通信
  // 所有功能都通过上下文系统管理，无需暴露任何方法或属性
</script>

<style lang="less" scoped>
  .chart-widget {
    position: relative;
    background: #fff;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.is-dragging {
      opacity: 0.8;
      transform: rotate(2deg);
    }
  }

  .drag-handle {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    cursor: move;
    user-select: none;

    .chart-title {
      margin-left: 8px;
      font-weight: 500;
      color: #333;
    }
  }

  .chart-container {
    position: relative;
    padding: 8px;
  }

  .echarts-wrapper {
    width: 100%;
    height: 100%;
  }
</style>
