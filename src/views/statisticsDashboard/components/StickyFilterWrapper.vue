<template>
  <div class="sticky-filter-wrapper">
    <!-- 原始筛选器容器 - 始终存在 -->
    <div ref="filterContainerRef" class="filter-container">
      <div ref="filterContentRef" class="filter-content original-filter" :class="{ 'is-hidden': isSticky }">
        <div class="filter-inner">
          <!-- 传递筛选器组件 -->
          <slot name="filter" :isSticky="false"></slot>
        </div>
      </div>
    </div>

    <!-- 吸顶筛选器副本 - 只在吸顶时显示 -->
    <Transition name="sticky-filter" @enter="onStickyEnter" @leave="onStickyLeave">
      <div v-if="isSticky || isExiting" class="filter-content sticky-filter" :style="stickyStyles">
        <div class="filter-inner">
          <!-- 传递筛选器组件副本 -->
          <slot name="filter" :isSticky="true"></slot>
        </div>

        <!-- 吸顶状态指示器 -->
        <div v-if="showStickyIndicator" class="sticky-indicator">
          <Icon icon="ant-design:pin-filled" />
          <span>筛选器已固定</span>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'StickyFilterWrapper',
  };
</script>
<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useStickyFilter } from '../hooks/useStickyFilter';

  interface Props {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 是否显示吸顶状态指示器 */
    showStickyIndicator?: boolean;
    /** 额外的顶部偏移量 */
    extraTopOffset?: number;
    /** 触发吸顶的额外阈值 */
    triggerThreshold?: number;
    /** 头部区域选择器 */
    headerSelector?: string;
    /** 导航栏选择器 */
    navSelector?: string;
    /** 自定义吸顶样式 */
    customStickyStyles?: Record<string, any>;
  }

  interface Emits {
    (e: 'sticky-change', isSticky: boolean): void;
    (e: 'dimensions-update', dimensions: { filterHeight: number; topOffset: number }): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    debug: false,
    showStickyIndicator: true,
    extraTopOffset: 0,
    triggerThreshold: 10,
    headerSelector: '.ant-layout-header',
    navSelector: '.ant-tabs-nav',
    customStickyStyles: () => ({}),
  });

  const emit = defineEmits<Emits>();

  // 动画控制状态
  const isExiting = ref(false);

  // 使用吸顶功能Hook
  const {
    filterContainerRef,
    filterContentRef,
    isSticky,
    filterHeight,
    originalWidth,
    originalLeft,
    topOffset,
    scrollTop,
    stickyThreshold,
    initStickyFilter,
    destroyStickyFilter,
    updateDimensions,
  } = useStickyFilter({
    debug: props.debug,
    extraTopOffset: props.extraTopOffset,
    triggerThreshold: props.triggerThreshold,
    headerSelector: props.headerSelector,
    navSelector: props.navSelector,
  });

  // 计算吸顶样式
  const stickyStyles = computed(() => {
    if (!isSticky.value) return {};

    const baseStyles = {
      position: 'fixed',
      top: `${topOffset.value + 10}px`,
      left: `${originalLeft.value}px`,
      width: `${originalWidth.value}px`,
      zIndex: 999,
      backgroundColor: '#fff',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
      borderRadius: '8px',
      backdropFilter: 'blur(8px)',
      border: '1px solid rgba(0, 0, 0, 0.06)',
    };

    // 合并自定义样式
    return { ...baseStyles, ...props.customStickyStyles };
  });

  // 监听吸顶状态变化
  watch(
    isSticky,
    (newIsSticky, oldIsSticky) => {
      emit('sticky-change', newIsSticky);

      // 控制退出动画
      if (!newIsSticky && oldIsSticky !== undefined) {
        // 退出吸顶状态时，延迟隐藏以显示退出动画
        isExiting.value = true;
        setTimeout(() => {
          isExiting.value = false;
        }, 200);
      } else {
        isExiting.value = false;
      }

      if (props.debug) {
        console.log(`📌 筛选器吸顶状态: ${newIsSticky ? '激活' : '取消'}`);
      }
    },
    { immediate: true }
  );

  // 监听尺寸变化
  watch(
    [filterHeight, topOffset],
    ([newFilterHeight, newTopOffset]) => {
      emit('dimensions-update', {
        filterHeight: newFilterHeight,
        topOffset: newTopOffset,
      });
    },
    { immediate: true }
  );

  // Transition 钩子函数
  const onStickyEnter = (el: Element) => {
    if (props.debug) {
      console.log('🎬 吸顶筛选器进入动画');
    }
  };

  const onStickyLeave = (el: Element) => {
    if (props.debug) {
      console.log('🎬 吸顶筛选器退出动画');
    }
  };

  // 暴露方法给父组件
  defineExpose({
    isSticky,
    filterHeight,
    topOffset,
    updateDimensions,
    scrollTop,
    stickyThreshold,
  });

  // 生命周期
  onMounted(async () => {
    await initStickyFilter();

    if (props.debug) {
      console.log('✅ 筛选器吸顶包装组件初始化完成');
    }
  });

  onUnmounted(() => {
    destroyStickyFilter();

    if (props.debug) {
      console.log('🗑️ 筛选器吸顶包装组件已销毁');
    }
  });
</script>

<style lang="less" scoped>
  .sticky-filter-wrapper {
    position: relative;

    .filter-container {
      .filter-content {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .filter-inner {
          transition: all 0.3s ease;
        }

        // 原始筛选器样式
        &.original-filter {
          transition: opacity 0.3s ease;

          &.is-hidden {
            opacity: 0.3;
            pointer-events: none;
          }
        }

        // 吸顶筛选器样式
        &.sticky-filter {
          .sticky-indicator {
            position: absolute;
            top: 12px;
            right: 24px;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.05));
            border: 1px solid rgba(24, 144, 255, 0.2);
            border-radius: 6px;
            color: #1890ff;
            font-size: 12px;
            font-weight: 500;
            z-index: 1;
            backdrop-filter: blur(4px);
            animation: fadeInSlide 0.3s ease-out;

            :deep(.anticon) {
              font-size: 12px;
              animation: pulse 2s infinite;
            }
          }

          .filter-inner {
          }
        }
      }
    }

    .filter-placeholder {
      background: transparent;
    }
  }

  // Vue Transition 动画类
  .sticky-filter-enter-active {
    transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .sticky-filter-leave-active {
    transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .sticky-filter-enter-from {
    transform: translateY(-20px);
    opacity: 0;
  }

  .sticky-filter-leave-to {
    transform: translateY(-10px);
    opacity: 0;
  }

  // 动画关键帧
  @keyframes fadeInSlide {
    0% {
      opacity: 0;
      transform: translateY(-10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  @keyframes stickyEnter {
    0% {
      transform: translateY(-20px);
      opacity: 0.8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    100% {
      transform: translateY(0);
      opacity: 1;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
  }

  @keyframes stickyExit {
    0% {
      opacity: 1;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    100% {
      opacity: 0.9;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        // 移动端保持全宽，但添加边距
        left: 8px !important;
        right: 8px !important;
        width: auto !important;
        border-radius: 12px !important;

        .filter-inner {
          padding: 12px 16px;
        }

        .sticky-indicator {
          right: 16px;
          padding: 4px 8px;
          font-size: 11px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        left: 4px !important;
        right: 4px !important;

        .filter-inner {
          padding: 8px 12px;
        }

        .sticky-indicator {
          display: none; // 超小屏幕隐藏指示器
        }
      }
    }
  }
</style>
