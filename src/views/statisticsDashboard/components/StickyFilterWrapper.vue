<template>
  <div class="sticky-filter-wrapper">
    <!-- 筛选器容器 -->
    <div ref="filterContainerRef" class="filter-container">
      <!-- 吸顶状态的筛选器 -->
      <div
        ref="filterContentRef"
        class="filter-content"
        :class="{
          'is-sticky': isSticky,
          'sticky-transition': true,
        }"
        :style="stickyStyles"
      >
        <div class="filter-inner">
          <!-- 传递筛选器组件 -->
          <slot name="filter" :isSticky="isSticky"></slot>
        </div>
      </div>
    </div>

    <!-- 占位符：防止吸顶时内容跳跃 -->
    <div v-show="isSticky" class="filter-placeholder" :style="placeholderStyles"></div>


    <!-- 调试按钮 -->
    <a-float-button v-if="debug" class="debug-button" type="primary" tooltip="显示调试信息" @click="debugVisible = !debugVisible">
      <template #icon>
        <Icon icon="ant-design:bug-outlined" />
      </template>
    </a-float-button>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'StickyFilterWrapper',
  };
</script>
<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useStickyFilter } from '../hooks/useStickyFilter';

  interface Props {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 是否显示吸顶状态指示器 */
    showStickyIndicator?: boolean;
    /** 额外的顶部偏移量 */
    extraTopOffset?: number;
    /** 触发吸顶的额外阈值 */
    triggerThreshold?: number;
    /** 头部区域选择器 */
    headerSelector?: string;
    /** 导航栏选择器 */
    navSelector?: string;
    /** 自定义吸顶样式 */
    customStickyStyles?: Record<string, any>;
  }

  interface Emits {
    (e: 'sticky-change', isSticky: boolean): void;
    (e: 'dimensions-update', dimensions: { filterHeight: number; topOffset: number }): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    debug: false,
    showStickyIndicator: true,
    extraTopOffset: 0,
    triggerThreshold: 10,
    headerSelector: '.ant-layout-header',
    navSelector: '.ant-tabs-nav',
    customStickyStyles: () => ({}),
  });

  const emit = defineEmits<Emits>();

  // 调试面板显示状态
  const debugVisible = ref(false);

  // 使用吸顶功能Hook
  const {
    filterContainerRef,
    filterContentRef,
    isSticky,
    filterHeight,
    originalWidth,
    originalLeft,
    topOffset,
    scrollTop,
    stickyThreshold,
    initStickyFilter,
    destroyStickyFilter,
    updateDimensions,
  } = useStickyFilter({
    debug: props.debug,
    extraTopOffset: props.extraTopOffset,
    triggerThreshold: props.triggerThreshold,
    headerSelector: props.headerSelector,
    navSelector: props.navSelector,
  });

  // 计算吸顶样式
  const stickyStyles = computed(() => {
    if (!isSticky.value) return {};

    const baseStyles = {
      position: 'fixed',
      top: `${topOffset.value + 10}px`,
      left: `${originalLeft.value}px`,
      width: `${originalWidth.value}px`,
      zIndex: 999,
      backgroundColor: '#fff',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
      borderRadius: '8px',
      transform: 'translateY(0)',
      transition: 'all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1)',
      animation: 'stickyEnter 0.4s ease-out',
      backdropFilter: 'blur(8px)',
      border: '1px solid rgba(0, 0, 0, 0.06)',
    };

    // 合并自定义样式
    return { ...baseStyles, ...props.customStickyStyles };
  });

  // 计算占位符样式
  const placeholderStyles = computed(() => {
    return {
      height: `${filterHeight.value}px`,
      transition: 'height 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
  });

  // 监听吸顶状态变化
  watch(
    isSticky,
    (newIsSticky) => {
      emit('sticky-change', newIsSticky);

      if (props.debug) {
        console.log(`📌 筛选器吸顶状态: ${newIsSticky ? '激活' : '取消'}`);
      }
    },
    { immediate: true }
  );

  // 监听尺寸变化
  watch(
    [filterHeight, topOffset],
    ([newFilterHeight, newTopOffset]) => {
      emit('dimensions-update', {
        filterHeight: newFilterHeight,
        topOffset: newTopOffset,
      });
    },
    { immediate: true }
  );

  // 暴露方法给父组件
  defineExpose({
    isSticky,
    filterHeight,
    topOffset,
    updateDimensions,
    scrollTop,
    stickyThreshold,
  });

  // 生命周期
  onMounted(async () => {
    await initStickyFilter();

    if (props.debug) {
      console.log('✅ 筛选器吸顶包装组件初始化完成');
    }
  });

  onUnmounted(() => {
    destroyStickyFilter();

    if (props.debug) {
      console.log('🗑️ 筛选器吸顶包装组件已销毁');
    }
  });
</script>

<style lang="less" scoped>
  .sticky-filter-wrapper {
    position: relative;

    .filter-container {
      .filter-content {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        transform: translateY(0);
        opacity: 1;

        &.sticky-transition {
          transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        &.is-sticky {
          // 吸顶状态的样式通过 stickyStyles 计算属性控制
          .sticky-indicator {
            position: absolute;
            top: 12px;
            right: 24px;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.05));
            border: 1px solid rgba(24, 144, 255, 0.2);
            border-radius: 6px;
            color: #1890ff;
            font-size: 12px;
            font-weight: 500;
            z-index: 1;
            backdrop-filter: blur(4px);
            animation: fadeInSlide 0.3s ease-out;

            :deep(.anticon) {
              font-size: 12px;
              animation: pulse 2s infinite;
            }
          }
        }

        .filter-inner {
          transition: all 0.3s ease;
        }
      }
    }

    .filter-placeholder {
      background: transparent;
    }

    .debug-panel {
      position: fixed;
      top: 120px;
      right: 24px;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 1001;
      min-width: 200px;

      .debug-title {
        font-weight: 600;
        margin-bottom: 12px;
        color: #1890ff;
      }

      .debug-info {
        margin-bottom: 12px;

        div {
          margin-bottom: 4px;
          font-size: 12px;
          color: #666;
        }
      }
    }

    .debug-button {
      position: fixed;
      bottom: 100px;
      right: 24px;
      z-index: 1000;
    }
  }

  // 动画关键帧
  @keyframes fadeInSlide {
    0% {
      opacity: 0;
      transform: translateY(-10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  @keyframes stickyEnter {
    0% {
      transform: translateY(-20px);
      opacity: 0.8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    100% {
      transform: translateY(0);
      opacity: 1;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        // 移动端保持全宽，但添加边距
        left: 8px !important;
        right: 8px !important;
        width: auto !important;
        border-radius: 12px !important;

        .filter-inner {
          padding: 12px 16px;
        }

        .sticky-indicator {
          right: 16px;
          padding: 4px 8px;
          font-size: 11px;
        }
      }

      .debug-panel {
        right: 16px;
        left: 16px;
        min-width: auto;
      }

      .debug-button {
        right: 16px;
      }
    }
  }

  @media (max-width: 480px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        left: 4px !important;
        right: 4px !important;

        .filter-inner {
          padding: 8px 12px;
        }

        .sticky-indicator {
          display: none; // 超小屏幕隐藏指示器
        }
      }
    }
  }
</style>
