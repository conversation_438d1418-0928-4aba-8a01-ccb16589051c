<template>
  <div class="sticky-filter-wrapper">
    <!-- 筛选器容器 -->
    <div ref="filterContainerRef" class="filter-container">
      <!-- 吸顶状态的筛选器 -->
      <div
        ref="filterContentRef"
        class="filter-content"
        :class="{
          'is-sticky': isSticky,
          'sticky-transition': true,
          'sticky-enter': isSticky && isAnimating,
          'sticky-exit': !isSticky && isExiting,
        }"
        :style="stickyStyles"
      >
        <div class="filter-inner">
          <!-- 传递筛选器组件 -->
          <slot name="filter" :isSticky="isSticky"></slot>
        </div>

        <!-- 吸顶状态指示器 -->
        <div v-if="isSticky && showStickyIndicator" class="sticky-indicator">
          <Icon icon="ant-design:pin-filled" />
          <span>筛选器已固定</span>
        </div>
      </div>
    </div>

    <!-- 占位符：防止吸顶时内容跳跃 -->
    <div v-show="isSticky" class="filter-placeholder" :style="placeholderStyles"></div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'StickyFilterWrapper',
  };
</script>
<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useStickyFilter } from '../hooks/useStickyFilter';

  interface Props {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 是否显示吸顶状态指示器 */
    showStickyIndicator?: boolean;
    /** 额外的顶部偏移量 */
    extraTopOffset?: number;
    /** 触发吸顶的额外阈值 */
    triggerThreshold?: number;
    /** 头部区域选择器 */
    headerSelector?: string;
    /** 导航栏选择器 */
    navSelector?: string;
    /** 自定义吸顶样式 */
    customStickyStyles?: Record<string, any>;
  }

  interface Emits {
    (e: 'sticky-change', isSticky: boolean): void;
    (e: 'dimensions-update', dimensions: { filterHeight: number; topOffset: number }): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    debug: false,
    showStickyIndicator: true,
    extraTopOffset: 0,
    triggerThreshold: 10,
    headerSelector: '.ant-layout-header',
    navSelector: '.ant-tabs-nav',
    customStickyStyles: () => ({}),
  });

  const emit = defineEmits<Emits>();

  // 动画控制状态
  const isAnimating = ref(false);
  const isExiting = ref(false);

  // 使用吸顶功能Hook
  const {
    filterContainerRef,
    filterContentRef,
    isSticky,
    filterHeight,
    originalWidth,
    originalLeft,
    topOffset,
    scrollTop,
    stickyThreshold,
    initStickyFilter,
    destroyStickyFilter,
    updateDimensions,
  } = useStickyFilter({
    debug: props.debug,
    extraTopOffset: props.extraTopOffset,
    triggerThreshold: props.triggerThreshold,
    headerSelector: props.headerSelector,
    navSelector: props.navSelector,
  });

  // 计算吸顶样式
  const stickyStyles = computed(() => {
    if (!isSticky.value) return {};

    const baseStyles = {
      position: 'fixed',
      top: `${topOffset.value + 10}px`,
      left: `${originalLeft.value}px`,
      width: `${originalWidth.value}px`,
      zIndex: 999,
      backgroundColor: '#fff',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
      borderRadius: '8px',
      backdropFilter: 'blur(8px)',
      border: '1px solid rgba(0, 0, 0, 0.06)',
    };

    // 合并自定义样式
    return { ...baseStyles, ...props.customStickyStyles };
  });

  // 计算占位符样式
  const placeholderStyles = computed(() => {
    return {
      height: `${filterHeight.value}px`,
      transition: 'height 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    };
  });

  // 监听吸顶状态变化
  watch(
    isSticky,
    (newIsSticky, oldIsSticky) => {
      emit('sticky-change', newIsSticky);

      // 控制动画
      if (newIsSticky) {
        // 进入吸顶状态
        isAnimating.value = true;
        isExiting.value = false;
        setTimeout(() => {
          isAnimating.value = false;
        }, 400);
      } else if (oldIsSticky !== undefined) {
        // 退出吸顶状态（避免初始化时触发）
        isExiting.value = true;
        isAnimating.value = false;
        setTimeout(() => {
          isExiting.value = false;
        }, 400);
      }

      if (props.debug) {
        console.log(`📌 筛选器吸顶状态: ${newIsSticky ? '激活' : '取消'}`);
      }
    },
    { immediate: true }
  );

  // 监听尺寸变化
  watch(
    [filterHeight, topOffset],
    ([newFilterHeight, newTopOffset]) => {
      emit('dimensions-update', {
        filterHeight: newFilterHeight,
        topOffset: newTopOffset,
      });
    },
    { immediate: true }
  );

  // 动画钩子函数
  const onEnter = (el: Element) => {
    if (props.debug) {
      console.log('🎬 进入动画开始');
    }
  };

  const onLeave = (el: Element) => {
    if (props.debug) {
      console.log('🎬 离开动画开始');
    }
  };

  // 暴露方法给父组件
  defineExpose({
    isSticky,
    filterHeight,
    topOffset,
    updateDimensions,
    scrollTop,
    stickyThreshold,
  });

  // 生命周期
  onMounted(async () => {
    await initStickyFilter();

    if (props.debug) {
      console.log('✅ 筛选器吸顶包装组件初始化完成');
    }
  });

  onUnmounted(() => {
    destroyStickyFilter();

    if (props.debug) {
      console.log('🗑️ 筛选器吸顶包装组件已销毁');
    }
  });
</script>

<style lang="less" scoped>
  .sticky-filter-wrapper {
    position: relative;

    .filter-container {
      .filter-content {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        transform: translateY(0);
        opacity: 1;

        &.sticky-transition {
          transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        &.sticky-exit {
          animation: stickyExit 0.4s ease-out;
        }

        &.is-sticky {
          // 吸顶状态的样式通过 stickyStyles 计算属性控制

          &.sticky-enter {
            animation: stickyEnter 0.4s ease-out;
          }

          .sticky-indicator {
            position: absolute;
            top: 12px;
            right: 24px;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.05));
            border: 1px solid rgba(24, 144, 255, 0.2);
            border-radius: 6px;
            color: #1890ff;
            font-size: 12px;
            font-weight: 500;
            z-index: 1;
            backdrop-filter: blur(4px);
            animation: fadeInSlide 0.3s ease-out;

            :deep(.anticon) {
              font-size: 12px;
              animation: pulse 2s infinite;
            }
          }
        }

        .filter-inner {
          transition: all 0.3s ease;
        }
      }
    }

    .filter-placeholder {
      background: transparent;
    }
  }

  // 动画关键帧
  @keyframes fadeInSlide {
    0% {
      opacity: 0;
      transform: translateY(-10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  @keyframes stickyEnter {
    0% {
      transform: translateY(-20px);
      opacity: 0.8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    100% {
      transform: translateY(0);
      opacity: 1;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
  }

  @keyframes stickyExit {
    0% {
      transform: translateY(0);
      opacity: 1;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    100% {
      transform: translateY(-10px);
      opacity: 0.8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        // 移动端保持全宽，但添加边距
        left: 8px !important;
        right: 8px !important;
        width: auto !important;
        border-radius: 12px !important;

        .filter-inner {
          padding: 12px 16px;
        }

        .sticky-indicator {
          right: 16px;
          padding: 4px 8px;
          font-size: 11px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        left: 4px !important;
        right: 4px !important;

        .filter-inner {
          padding: 8px 12px;
        }

        .sticky-indicator {
          display: none; // 超小屏幕隐藏指示器
        }
      }
    }
  }
</style>
