<template>
  <div class="sticky-filter-wrapper">
    <!-- 原始筛选器容器 - 始终存在 -->
    <div ref="filterContainerRef" class="filter-container">
      <div ref="filterContentRef" class="filter-content original-filter" :class="{ 'is-hidden': isSticky }">
        <div class="filter-inner">
          <!-- 传递筛选器组件 -->
          <slot name="filter" :isSticky="false"></slot>
        </div>
      </div>
    </div>

    <!-- 吸顶筛选器副本 - 只在吸顶时显示 -->
    <div
      v-show="isSticky"
      class="filter-content sticky-filter"
      :style="stickyStyles"
    >
      <div class="filter-inner">
        <!-- 传递筛选器组件副本 -->
        <slot name="filter" :isSticky="true"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'StickyFilterWrapper',
  };
</script>
<script setup lang="ts">
  import { computed, onMounted, onUnmounted, watch } from 'vue';
  import { debounce } from 'lodash-es';
  import { useStickyFilter } from '../hooks/useStickyFilter';

  interface Props {
    /** 额外的顶部偏移量 */
    extraTopOffset?: number;
    /** 触发吸顶的额外阈值 */
    triggerThreshold?: number;
    /** 头部区域选择器 */
    headerSelector?: string;
    /** 导航栏选择器 */
    navSelector?: string;
    /** 自定义吸顶样式 */
    customStickyStyles?: Record<string, any>;
  }

  interface Emits {
    (e: 'sticky-change', isSticky: boolean): void;
    (e: 'dimensions-update', dimensions: { filterHeight: number; topOffset: number }): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    extraTopOffset: 0,
    triggerThreshold: 10,
    headerSelector: '.ant-layout-header',
    navSelector: '.ant-tabs-nav',
    customStickyStyles: () => ({}),
  });

  const emit = defineEmits<Emits>();

  // 使用吸顶功能Hook
  const {
    filterContainerRef,
    filterContentRef,
    isSticky,
    filterHeight,
    originalWidth,
    originalLeft,
    topOffset,
    scrollTop,
    stickyThreshold,
    initStickyFilter,
    destroyStickyFilter,
    updateDimensions,
  } = useStickyFilter({
    extraTopOffset: props.extraTopOffset,
    triggerThreshold: props.triggerThreshold,
    headerSelector: props.headerSelector,
    navSelector: props.navSelector,
  });

  // 计算吸顶样式
  const stickyStyles = computed(() => {
    if (!isSticky.value) return {};

    const baseStyles = {
      position: 'fixed' as const,
      top: `${topOffset.value + 10}px`,
      left: `${originalLeft.value}px`,
      width: `${originalWidth.value}px`,
      zIndex: 999,
      backgroundColor: '#fff',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
      borderRadius: '8px',
      backdropFilter: 'blur(8px)',
      border: '1px solid rgba(0, 0, 0, 0.06)',
      transition: 'all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1)',
      animation: 'stickyEnter 0.4s ease-out',
    };

    // 合并自定义样式
    return { ...baseStyles, ...props.customStickyStyles };
  });

  // 监听吸顶状态变化 - 使用防抖优化
  const debouncedStickyChange = debounce((isSticky: boolean) => {
    emit('sticky-change', isSticky);
  }, 100);

  watch(isSticky, debouncedStickyChange, { immediate: true });

  // 监听尺寸变化
  watch(
    [filterHeight, topOffset],
    ([newFilterHeight, newTopOffset]) => {
      emit('dimensions-update', {
        filterHeight: newFilterHeight,
        topOffset: newTopOffset,
      });
    },
    { immediate: true }
  );

  // 暴露方法给父组件
  defineExpose({
    isSticky,
    filterHeight,
    topOffset,
    updateDimensions,
    scrollTop,
    stickyThreshold,
  });

  // 生命周期
  onMounted(async () => {
    await initStickyFilter();
  });

  onUnmounted(() => {
    destroyStickyFilter();
  });
</script>

<style lang="less" scoped>
  .sticky-filter-wrapper {
    position: relative;

    .filter-container {
      .filter-content {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .filter-inner {
          transition: all 0.3s ease;
        }

        // 原始筛选器样式
        &.original-filter {
          transition: opacity 0.3s ease;

          &.is-hidden {
            opacity: 0.3;
            pointer-events: none;
          }
        }
      }
    }

    .filter-placeholder {
      background: transparent;
    }
  }

  // 吸顶进入动画
  @keyframes stickyEnter {
    0% {
      transform: translateY(-20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        // 移动端保持全宽，但添加边距
        left: 8px !important;
        right: 8px !important;
        width: auto !important;
        border-radius: 12px !important;

        .filter-inner {
          padding: 12px 16px;
        }

        .sticky-indicator {
          right: 16px;
          padding: 4px 8px;
          font-size: 11px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        left: 4px !important;
        right: 4px !important;

        .filter-inner {
          padding: 8px 12px;
        }

        .sticky-indicator {
          display: none; // 超小屏幕隐藏指示器
        }
      }
    }
  }
</style>
