import { ChartConfig, ChartDataItem, DrillDownConfig } from '../types/statisticDashboard';

/**
 * 销售数据 - 支持三层下探：区域 -> 城市 -> 门店
 */
export const salesData: ChartDataItem[] = [
  {
    name: '北区',
    value: 1000,
    region: 'North',
    children: [
      {
        name: '北京',
        value: 600,
        city: 'Beijing',
        region: 'North',
        children: [
          { name: '朝阳门店', value: 300, store: 'Store1', city: 'Beijing' },
          { name: '海淀门店', value: 200, store: 'Store2', city: 'Beijing' },
          { name: '西城门店', value: 100, store: 'Store3', city: 'Beijing' },
        ],
      },
      {
        name: '天津',
        value: 400,
        city: 'Tianjin',
        region: 'North',
        children: [
          { name: '和平门店', value: 250, store: 'Store4', city: 'Tianjin' },
          { name: '河西门店', value: 150, store: 'Store5', city: 'Tianjin' },
        ],
      },
    ],
  },
  {
    name: '南区',
    value: 800,
    region: 'South',
    children: [
      {
        name: '上海',
        value: 500,
        city: 'Shanghai',
        region: 'South',
        children: [
          { name: '浦东门店', value: 300, store: 'Store6', city: 'Shanghai' },
          { name: '徐汇门店', value: 200, store: 'Store7', city: 'Shanghai' },
        ],
      },
      {
        name: '广州',
        value: 300,
        city: 'Guangzhou',
        region: 'South',
        children: [
          { name: '天河门店', value: 180, store: 'Store8', city: 'Guangzhou' },
          { name: '越秀门店', value: 120, store: 'Store9', city: 'Guangzhou' },
        ],
      },
    ],
  },
  {
    name: '东区',
    value: 600,
    region: 'East',
    children: [
      {
        name: '杭州',
        value: 350,
        city: 'Hangzhou',
        region: 'East',
        children: [
          { name: '西湖门店', value: 200, store: 'Store10', city: 'Hangzhou' },
          { name: '滨江门店', value: 150, store: 'Store11', city: 'Hangzhou' },
        ],
      },
      {
        name: '南京',
        value: 250,
        city: 'Nanjing',
        region: 'East',
        children: [
          { name: '鼓楼门店', value: 150, store: 'Store12', city: 'Nanjing' },
          { name: '玄武门店', value: 100, store: 'Store13', city: 'Nanjing' },
        ],
      },
    ],
  },
];

/**
 * 产品类别数据 - 支持两层下探：类别 -> 子类别
 */
export const categoryData: ChartDataItem[] = [
  {
    name: '电子产品',
    value: 1200,
    category: 'Electronics',
    children: [
      { name: '手机', value: 500, subcategory: 'Phone', category: 'Electronics' },
      { name: '电脑', value: 400, subcategory: 'Computer', category: 'Electronics' },
      { name: '平板', value: 300, subcategory: 'Tablet', category: 'Electronics' },
    ],
  },
  {
    name: '服装',
    value: 800,
    category: 'Clothing',
    children: [
      { name: '男装', value: 350, subcategory: 'MenClothing', category: 'Clothing' },
      { name: '女装', value: 300, subcategory: 'WomenClothing', category: 'Clothing' },
      { name: '童装', value: 150, subcategory: 'KidsClothing', category: 'Clothing' },
    ],
  },
  {
    name: '家居',
    value: 600,
    category: 'Home',
    children: [
      { name: '家具', value: 300, subcategory: 'Furniture', category: 'Home' },
      { name: '装饰', value: 200, subcategory: 'Decoration', category: 'Home' },
      { name: '厨具', value: 100, subcategory: 'Kitchen', category: 'Home' },
    ],
  },
];

/**
 * 月度趋势数据 - 折线图数据
 */
export const monthlyTrendData: ChartDataItem[] = [
  { name: '1月', value: 820 },
  { name: '2月', value: 932 },
  { name: '3月', value: 901 },
  { name: '4月', value: 934 },
  { name: '5月', value: 1290 },
  { name: '6月', value: 1330 },
  { name: '7月', value: 1320 },
  { name: '8月', value: 1200 },
  { name: '9月', value: 1100 },
  { name: '10月', value: 1400 },
  { name: '11月', value: 1500 },
  { name: '12月', value: 1600 },
];

/**
 * 散点图数据 - 销售额与利润率关系
 */
export const scatterData: ChartDataItem[] = [
  { name: '产品A', value: [120, 15.2], sales: 120, profit: 15.2 },
  { name: '产品B', value: [180, 22.8], sales: 180, profit: 22.8 },
  { name: '产品C', value: [95, 8.5], sales: 95, profit: 8.5 },
  { name: '产品D', value: [220, 28.5], sales: 220, profit: 28.5 },
  { name: '产品E', value: [160, 18.9], sales: 160, profit: 18.9 },
  { name: '产品F', value: [140, 16.7], sales: 140, profit: 16.7 },
  { name: '产品G', value: [200, 25.3], sales: 200, profit: 25.3 },
  { name: '产品H', value: [110, 12.4], sales: 110, profit: 12.4 },
  { name: '产品I', value: [190, 23.1], sales: 190, profit: 23.1 },
  { name: '产品J', value: [170, 19.8], sales: 170, profit: 19.8 },
];

/**
 * 雷达图数据 - 产品综合评价
 */
export const radarData: ChartDataItem[] = [
  {
    name: '产品A',
    value: [85, 90, 78, 92, 88, 85],
    indicators: ['质量', '价格', '服务', '创新', '品牌', '用户体验'],
  },
  {
    name: '产品B',
    value: [78, 85, 88, 85, 90, 82],
    indicators: ['质量', '价格', '服务', '创新', '品牌', '用户体验'],
  },
  {
    name: '产品C',
    value: [92, 75, 85, 88, 82, 90],
    indicators: ['质量', '价格', '服务', '创新', '品牌', '用户体验'],
  },
];

/**
 * 漏斗图数据 - 销售转化漏斗
 */
export const funnelData: ChartDataItem[] = [
  { name: '访问量', value: 10000 },
  { name: '浏览产品', value: 6000 },
  { name: '加入购物车', value: 3000 },
  { name: '提交订单', value: 1500 },
  { name: '支付成功', value: 1200 },
];

/**
 * 面积图数据 - 多产品销售趋势对比
 */
export const areaData: ChartDataItem[] = [
  { name: '1月', value: 820, productA: 820, productB: 932, productC: 450 },
  { name: '2月', value: 932, productA: 932, productB: 1200, productC: 520 },
  { name: '3月', value: 901, productA: 901, productB: 1100, productC: 480 },
  { name: '4月', value: 934, productA: 934, productB: 1300, productC: 550 },
  { name: '5月', value: 1290, productA: 1290, productB: 1400, productC: 680 },
  { name: '6月', value: 1330, productA: 1330, productB: 1500, productC: 720 },
  { name: '7月', value: 1320, productA: 1320, productB: 1450, productC: 700 },
  { name: '8月', value: 1200, productA: 1200, productB: 1350, productC: 650 },
  { name: '9月', value: 1100, productA: 1100, productB: 1250, productC: 600 },
  { name: '10月', value: 1400, productA: 1400, productB: 1600, productC: 800 },
  { name: '11月', value: 1500, productA: 1500, productB: 1700, productC: 850 },
  { name: '12月', value: 1600, productA: 1600, productB: 1800, productC: 900 },
];

/**
 * 热力图数据 - 时间段销售热力分布
 */
export const heatmapData: ChartDataItem[] = [
  { name: '周一-8时', value: [0, 0, 120], day: 0, hour: 0, sales: 120 },
  { name: '周一-9时', value: [0, 1, 180], day: 0, hour: 1, sales: 180 },
  { name: '周一-10时', value: [0, 2, 220], day: 0, hour: 2, sales: 220 },
  { name: '周一-11时', value: [0, 3, 280], day: 0, hour: 3, sales: 280 },
  { name: '周一-12时', value: [0, 4, 350], day: 0, hour: 4, sales: 350 },
  { name: '周二-8时', value: [1, 0, 110], day: 1, hour: 0, sales: 110 },
  { name: '周二-9时', value: [1, 1, 170], day: 1, hour: 1, sales: 170 },
  { name: '周二-10时', value: [1, 2, 210], day: 1, hour: 2, sales: 210 },
  { name: '周二-11时', value: [1, 3, 270], day: 1, hour: 3, sales: 270 },
  { name: '周二-12时', value: [1, 4, 340], day: 1, hour: 4, sales: 340 },
  { name: '周三-8时', value: [2, 0, 130], day: 2, hour: 0, sales: 130 },
  { name: '周三-9时', value: [2, 1, 190], day: 2, hour: 1, sales: 190 },
  { name: '周三-10时', value: [2, 2, 240], day: 2, hour: 2, sales: 240 },
  { name: '周三-11时', value: [2, 3, 300], day: 2, hour: 3, sales: 300 },
  { name: '周三-12时', value: [2, 4, 380], day: 2, hour: 4, sales: 380 },
  { name: '周四-8时', value: [3, 0, 125], day: 3, hour: 0, sales: 125 },
  { name: '周四-9时', value: [3, 1, 185], day: 3, hour: 1, sales: 185 },
  { name: '周四-10时', value: [3, 2, 235], day: 3, hour: 2, sales: 235 },
  { name: '周四-11时', value: [3, 3, 295], day: 3, hour: 3, sales: 295 },
  { name: '周四-12时', value: [3, 4, 365], day: 3, hour: 4, sales: 365 },
];

/**
 * 仪表盘数据 - 关键指标监控
 */
export const gaugeData: ChartDataItem[] = [
  { name: '销售完成率', value: 78.5, max: 100, unit: '%' },
  { name: '客户满意度', value: 92.3, max: 100, unit: '%' },
  { name: '库存周转率', value: 65.8, max: 100, unit: '%' },
];

/**
 * 数据下探配置
 */
export const salesDrillConfig: DrillDownConfig = {
  enabled: true,
  currentLevel: 0,
  maxLevel: 2,
  levels: [
    {
      level: 0,
      dataKey: 'region',
      titleField: 'name',
      valueField: 'value',
      colorField: 'region',
    },
    {
      level: 1,
      dataKey: 'city',
      titleField: 'name',
      valueField: 'value',
      colorField: 'city',
      parentKey: 'region',
    },
    {
      level: 2,
      dataKey: 'store',
      titleField: 'name',
      valueField: 'value',
      colorField: 'store',
      parentKey: 'city',
    },
  ],
};

export const categoryDrillConfig: DrillDownConfig = {
  enabled: true,
  currentLevel: 0,
  maxLevel: 1,
  levels: [
    {
      level: 0,
      dataKey: 'category',
      titleField: 'name',
      valueField: 'value',
      colorField: 'category',
    },
    {
      level: 1,
      dataKey: 'subcategory',
      titleField: 'name',
      valueField: 'value',
      colorField: 'subcategory',
      parentKey: 'category',
    },
  ],
};

/**
 * 图表配置
 */
export const chartConfigs: ChartConfig[] = [
  {
    id: 'sales-bar',
    type: 'bar',
    title: '区域销售额统计',
    dataSource: 'sales',
    options: {
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
    },
    drillDown: salesDrillConfig,
    size: { width: 600, height: 400 },
    position: { x: 0, y: 0 },
  },
  {
    id: 'category-pie',
    type: 'pie',
    title: '产品类别分布',
    dataSource: 'category',
    options: {
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      legend: {
        orient: 'vertical',
        left: 'left',
        // 🎨 保持图标文字对齐一致
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 20,
        padding: [5, 10],
        textStyle: {
          fontSize: 12,
          lineHeight: 14,
          padding: [0, 0, 0, 5],
        },
      },
    },
    drillDown: categoryDrillConfig,
    size: { width: 600, height: 400 },
    position: { x: 0, y: 0 },
  },
  {
    id: 'trend-line',
    type: 'line',
    title: '月度销售趋势',
    dataSource: 'trend',
    options: {
      color: ['#5470c6'],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        boundaryGap: false,
      },
    },
    size: { width: 600, height: 400 },
    position: { x: 0, y: 0 },
  },
  {
    id: 'sales-scatter',
    type: 'scatter',
    title: '销售额与利润率关系',
    dataSource: 'scatter',
    options: {
      color: ['#5470c6'],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        name: '销售额(万元)',
        nameLocation: 'middle',
        nameGap: 30,
      },
      yAxis: {
        name: '利润率(%)',
        nameLocation: 'middle',
        nameGap: 40,
      },
      tooltip: {
        formatter: '{b}<br/>销售额: {c[0]}万元<br/>利润率: {c[1]}%',
      },
    },
    size: { width: 600, height: 400 },
    position: { x: 0, y: 0 },
  },
  {
    id: 'product-radar',
    type: 'radar',
    title: '产品综合评价雷达图',
    dataSource: 'radar',
    options: {
      color: ['#5470c6', '#91cc75', '#fac858'],
      radar: {
        indicator: [
          { name: '质量', max: 100 },
          { name: '价格', max: 100 },
          { name: '服务', max: 100 },
          { name: '创新', max: 100 },
          { name: '品牌', max: 100 },
          { name: '用户体验', max: 100 },
        ],
        radius: '60%',
      },
      legend: {
        bottom: 10,
        // 🎨 保持图标文字对齐一致
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 20,
        padding: [5, 10],
        textStyle: {
          fontSize: 12,
          lineHeight: 14,
          padding: [0, 0, 0, 5],
        },
      },
    },
    size: { width: 600, height: 400 },
    position: { x: 0, y: 0 },
  },
  {
    id: 'conversion-funnel',
    type: 'funnel',
    title: '销售转化漏斗',
    dataSource: 'funnel',
    options: {
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      legend: {
        bottom: 10,
        // 🎨 保持图标文字对齐一致
        itemWidth: 14,
        itemHeight: 14,
        itemGap: 20,
        padding: [5, 10],
        textStyle: {
          fontSize: 12,
          lineHeight: 14,
          padding: [0, 0, 0, 5],
        },
      },
      funnel: {
        left: '10%',
        top: 60,
        width: '80%',
        height: '60%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid',
          },
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
        },
      },
    },
    size: { width: 600, height: 400 },
    position: { x: 0, y: 0 },
  },
  {
    id: 'trend-area',
    type: 'line',
    title: '多产品销售趋势面积图',
    dataSource: 'area',
    options: {
      color: ['#5470c6', '#91cc75', '#fac858'],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        boundaryGap: false,
      },
      legend: {
        top: 10,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
      },
    },
    size: { width: 600, height: 400 },
    position: { x: 0, y: 0 },
  },
  {
    id: 'sales-heatmap',
    type: 'scatter', // 使用scatter类型来实现热力图效果
    title: '销售时间热力分布图',
    dataSource: 'heatmap',
    options: {
      color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026'],
      grid: {
        left: '3%',
        right: '7%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        splitArea: {
          show: true,
        },
      },
      yAxis: {
        type: 'category',
        data: ['8时', '9时', '10时', '11时', '12时', '13时', '14时', '15时', '16时', '17时', '18时'],
        splitArea: {
          show: true,
        },
      },
      visualMap: {
        min: 0,
        max: 400,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
      },
      tooltip: {
        position: 'top',
      },
    },
    size: { width: 600, height: 400 },
    position: { x: 0, y: 0 },
  },
  {
    id: 'kpi-gauge',
    type: 'scatter', // 使用scatter类型，在ECharts组件中特殊处理为gauge
    title: '关键指标仪表盘',
    dataSource: 'gauge',
    options: {
      color: ['#5470c6', '#91cc75', '#fac858'],
      tooltip: {
        formatter: '{b}: {c}%',
      },
    },
    size: { width: 600, height: 400 },
    position: { x: 0, y: 0 },
  },
];
