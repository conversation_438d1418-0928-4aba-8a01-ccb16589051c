// 线索来源 mock数据

import { ChannelConfig, ChartConfig, ChartDataItem, MonthlyChannelData } from '../types/statisticDashboard';
import { calculatePercentage, createTooltipFormatter, generateChartDataItem } from '../utils';

import { useI18n } from '../../../hooks/web/useI18n';

const { t } = useI18n('common');

/**
 * UTM渠道配置定义 - utm_source维度
 */
export const utmSourceChannelConfig: ChannelConfig[] = [
  {
    key: 'uol',
    name: 'UOL',
    color: '#5470c6', // 蓝色
  },
  {
    key: 'google',
    name: 'Google',
    color: '#91cc75', // 绿色
  },
  {
    key: 'weach',
    name: 'Weach',
    color: '#fac858', // 黄色
  },
  {
    key: 'metaAds',
    name: 'Meta Ads',
    color: '#ee6666', // 红色
  },
];

/**
 * UTM渠道配置定义 - utm_medium维度
 */
export const utmMediumChannelConfig: ChannelConfig[] = [
  {
    key: 'cpc',
    name: 'CPC',
    color: '#5470c6', // 蓝色
  },
  {
    key: 'social',
    name: 'Social',
    color: '#91cc75', // 绿色
  },
  {
    key: 'email',
    name: 'Email',
    color: '#fac858', // 黄色
  },
  {
    key: 'organic',
    name: 'Organic',
    color: '#ee6666', // 红色
  },
  {
    key: 'referral',
    name: 'Referral',
    color: '#73c0de', // 粉色系
  },
];

/**
 * UTM Source数据（utm_source维度）
 */
export const utmSourceData: MonthlyChannelData[] = [
  {
    name: '2025-07-24',
    value: 125,
    type: 'period',
    channels: {
      uol: 45,
      google: 32,
      weach: 18,
      metaAds: 15,
    },
  },
  {
    name: '2025-07-25',
    value: 135,
    type: 'period',
    channels: {
      uol: 48,
      google: 35,
      weach: 20,
      metaAds: 17,
    },
  },
  {
    name: '2025-07-26',
    value: 145,
    type: 'period',
    channels: {
      uol: 50,
      google: 38,
      weach: 22,
      metaAds: 20,
    },
  },
  {
    name: '2025-07-27',
    value: 146,
    type: 'period',
    channels: {
      uol: 48,
      google: 42,
      weach: 25,
      metaAds: 16,
    },
  },
  {
    name: '2025-07-28',
    value: 155,
    type: 'period',
    channels: {
      uol: 55,
      google: 40,
      weach: 28,
      metaAds: 17,
    },
  },
  {
    name: '2025-07-29',
    value: 175,
    type: 'period',
    channels: {
      uol: 58,
      google: 45,
      weach: 30,
      metaAds: 22,
    },
  },
  {
    name: '2025-07-30',
    value: 187,
    type: 'period',
    channels: {
      uol: 62,
      google: 48,
      weach: 32,
      metaAds: 25,
    },
  },
];

/**
 * UTM Medium数据（utm_medium维度）
 */
export const utmMediumData: MonthlyChannelData[] = [
  {
    name: '2025-07-24',
    value: 125,
    type: 'period',
    channels: {
      cpc: 50,
      social: 30,
      email: 20,
      organic: 15,
      referral: 10,
    },
  },
  {
    name: '2025-07-25',
    value: 135,
    type: 'period',
    channels: {
      cpc: 55,
      social: 32,
      email: 22,
      organic: 16,
      referral: 10,
    },
  },
  {
    name: '2025-07-26',
    value: 145,
    type: 'period',
    channels: {
      cpc: 58,
      social: 35,
      email: 24,
      organic: 18,
      referral: 10,
    },
  },
  {
    name: '2025-07-27',
    value: 146,
    type: 'period',
    channels: {
      cpc: 56,
      social: 38,
      email: 26,
      organic: 16,
      referral: 10,
    },
  },
  {
    name: '2025-07-28',
    value: 155,
    type: 'period',
    channels: {
      cpc: 62,
      social: 40,
      email: 28,
      organic: 15,
      referral: 10,
    },
  },
  {
    name: '2025-07-29',
    value: 175,
    type: 'period',
    channels: {
      cpc: 70,
      social: 45,
      email: 30,
      organic: 20,
      referral: 10,
    },
  },
  {
    name: '2025-07-30',
    value: 187,
    type: 'period',
    channels: {
      cpc: 75,
      social: 48,
      email: 32,
      organic: 22,
      referral: 10,
    },
  },
];

// ==================== 具体图表的Tooltip配置 ====================

/**
 * UTM图表tooltip格式化函数
 */
const formatUtmTooltip = createTooltipFormatter({
  showTotal: true,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: t('totalTrend') },
  totalLabel: t('total'),
});

/**
 * 生成UTM图表配置（支持utm_source和utm_medium维度切换）
 */
export const generateUtmChartConfig = (dataSource: 'utmSource' | 'utmMedium' = 'utmSource'): ChartConfig => {
  const isUtmSource = dataSource === 'utmSource';
  const data = isUtmSource ? utmSourceData : utmMediumData;
  const channelConfig = isUtmSource ? utmSourceChannelConfig : utmMediumChannelConfig;
  const title = isUtmSource ? 'utm_source' : 'utm_medium';

  // 生成堆叠系列配置（包含所有渠道）
  const stackSeries = channelConfig.map((channelItem) => ({
    name: channelItem.name,
    type: 'bar',
    stack: 'utm', // 堆叠配置
    data: data.map((item) => {
      const value = item.channels[channelItem.key] || 0;
      const percent = item.value > 0 ? calculatePercentage(value, item.value) : '0.0';
      return generateChartDataItem(item.name, value, percent, {
        channelKey: channelItem.key,
      });
    }),
    itemStyle: {
      color: channelItem.color,
    },
    label: {
      show: false, // 在堆叠图中通常不显示标签，避免重叠
    },
  }));

  // 生成总量趋势线系列配置（连接堆叠柱状图顶部）
  const trendSeries = {
    name: t('totalTrend'),
    type: 'line',
    data: data.map((item, index) => {
      // 计算所有渠道的总和
      const totalValue = channelConfig.reduce((sum, config) => sum + (item.channels[config.key] || 0), 0);
      return {
        name: item.name,
        value: totalValue, // 使用计算出的总和
        // 只在头尾显示标签
        label: {
          show: index === 0 || index === data.length - 1,
          position: 'top',
          formatter: '{c}',
          fontSize: 12,
          color: '#8B0000', // 深红色标签
        },
      };
    }),
    itemStyle: {
      color: '#8B0000', // 深红色数据点
    },
    lineStyle: {
      color: '#8B0000', // 深红色线条
      width: 2,
    },
    symbol: 'circle', // 使用圆形符号 是否有空心的圆型符号
    symbolSize: 6,
    yAxisIndex: 0, // 使用同一个Y轴
  };

  // 合并所有系列
  const series = [...stackSeries, trendSeries];

  return {
    id: 'utmChart',
    type: 'bar',
    title,
    dataSource,
    // 添加自定义属性来标识这是一个可切换的图表
    customProps: {
      switchable: true,
      currentDataSource: dataSource,
      alternativeDataSource: isUtmSource ? 'utmMedium' : 'utmSource',
      alternativeTitle: isUtmSource ? 'utm_medium' : 'utm_source',
    },
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      color: [...channelConfig.map((config) => config.color), '#8B0000'], // 包含所有渠道和深红色趋势线
      grid: {
        left: '4%',
        right: '4%',
        bottom: '15%', // 为底部legend留出更多空间
        containLabel: true,
      },
      legend: {
        data: [...channelConfig.map((config) => config.name), t('totalTrend')], // 包含所有渠道和趋势线
        bottom: '5%', // legend放在底部
        left: 'center',
        type: 'scroll', // 如果legend项太多，支持滚动
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.name),
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 30, // 倾斜30度
        },
      },
      yAxis: {
        type: 'value',
        name: t('numberOfClues'),
      },
      series: series as any,
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: formatUtmTooltip, // 使用UTM专用的tooltip函数
      },
    },
    size: { height: 450 },
    position: { x: 0, y: 0 },
  };
};

/** UTM图表配置（默认使用utm_source数据） */
export const utmChartConfigs: ChartConfig[] = [generateUtmChartConfig('utmSource')];

/**
 * 线索有效性数据（兼容ChartDataItem接口）- 7天数据
 * 使用ChartDataItem标准结构，额外属性存储在扩展字段中
 */
export const clueEffectivenessData: ChartDataItem[] = [
  {
    name: '2025-07-24',
    value: 120, // 线索总数作为主value
    type: 'period',
    totalClues: 120,
    effectiveClues: 90,
    effectivenessRate: 75.0,
  },
  {
    name: '2025-07-25',
    value: 135,
    type: 'period',
    totalClues: 135,
    effectiveClues: 105,
    effectivenessRate: 77.8,
  },
  {
    name: '2025-07-26',
    value: 150,
    type: 'period',
    totalClues: 150,
    effectiveClues: 118,
    effectivenessRate: 78.7,
  },
  {
    name: '2025-07-27',
    value: 110,
    type: 'period',
    totalClues: 110,
    effectiveClues: 85,
    effectivenessRate: 77.3,
  },
  {
    name: '2025-07-28',
    value: 140,
    type: 'period',
    totalClues: 140,
    effectiveClues: 105,
    effectivenessRate: 75.0,
  },
  {
    name: '2025-07-29',
    value: 165,
    type: 'period',
    totalClues: 165,
    effectiveClues: 120,
    effectivenessRate: 72.7,
  },
  {
    name: '2025-07-30',
    value: 175,
    type: 'period',
    totalClues: 175,
    effectiveClues: 120,
    effectivenessRate: 68.6,
  },
];

/**
 * 线索有效性tooltip格式化函数
 */
const formatEffectivenessTooltip = createTooltipFormatter({
  extraInfoProvider: (axisValue: string) => {
    // 查找对应日期的数据来获取有效率
    const dayData = clueEffectivenessData.find((item: any) => item.name === axisValue);
    const effectivenessRate = dayData?.effectivenessRate || 0;
    return `${t('efficient')}: ${effectivenessRate}%`;
  },
});

/**
 * 生成线索有效性折线图配置
 */
export const generateClueEffectivenessChartConfig = (): ChartConfig => {
  const data = clueEffectivenessData;

  // 生成线索总量系列
  const totalCluesSeries = {
    name: t('totalNumberOfLeads'),
    type: 'line',
    data: data.map((item: any) => ({
      name: item.name,
      value: item.totalClues, // 使用totalClues字段
    })),
    itemStyle: {
      color: '#5470c6', // 蓝色
    },
    lineStyle: {
      color: '#5470c6',
      width: 2,
    },
    symbol: 'circle',
    symbolSize: 6,
  };

  // 生成有效线索总量系列
  const effectiveCluesSeries = {
    name: t('totalNumberOfEffectiveClues'),
    type: 'line',
    data: data.map((item: any) => ({
      name: item.name,
      value: item.effectiveClues, // 使用effectiveClues字段
    })),
    itemStyle: {
      color: '#91cc75', // 绿色
    },
    lineStyle: {
      color: '#91cc75',
      width: 2,
    },
    symbol: 'circle',
    symbolSize: 6,
  };

  return {
    id: 'clueEffectiveness',
    type: 'line',
    title: t('clueValidity'),
    dataSource: 'clueEffectiveness',
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      color: ['#5470c6', '#91cc75'], // 蓝色和绿色
      grid: {
        left: '4%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
      legend: {
        data: [t('totalNumberOfLeads'), t('totalNumberOfEffectiveClues')],
        bottom: '5%',
        left: 'center',
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.name),
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 30, // 倾斜30度
        },
      },
      yAxis: {
        type: 'value',
        name: t('numberOfClues'),
        min: 0,
        max: 180,
      },
      series: [totalCluesSeries, effectiveCluesSeries] as any,
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
        formatter: formatEffectivenessTooltip, // 使用专用的tooltip格式化函数
      },
    },
    size: { height: 450 },
    position: { x: 0, y: 0 },
  };
};

/** 线索有效性图表配置 */
export const clueEffectivenessChartConfigs: ChartConfig[] = [generateClueEffectivenessChartConfig()];
