<template>
  <a-card>
    <BasicForm @register="registerForm" @submit="handleSubmit" @cancel="handleCancel"> </BasicForm>
  </a-card>
</template>

<script setup lang="ts">
  import { useForm, BasicForm, FormSchema } from '/@/components/Form';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { router } from '/@/router';
  import { useRoutePageType } from '/@/hooks/route';
  import { PageTypeEnum } from '/@/enums/common';
  import { onMounted, ref } from 'vue';
  import { awaitTo } from '@ruqi/utils-admin/esm/tools';
  import { featchAddProvince, featchUpdateProvince, featchProvinceDetail, StateInfo } from '/@/api/basicData/nationCity';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { isUndefined } from 'lodash-es';
  import { useSessionStorageState } from 'vue-hooks-plus';
  import { useTabs } from '/@/hooks/web/useTabs';
  import { useRegionNationTreeOptions } from '/@/hooks/common/useRegionNationTreeOptions';
  const { t } = useI18n('common');

  const pageType = useRoutePageType();
  const { getData: getRegionNationTreeData } = useRegionNationTreeOptions();
  const [stateinformation] = useSessionStorageState('StateItem', {
    defaultValue: {} as StateInfo,
  });

  const id = Number(stateinformation.value?.id);
  const disabled = pageType === PageTypeEnum.VIEW;

  const formSchemas: FormSchema[] = [
    {
      label: t('region'),
      field: 'regionName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: ' ',
        disabled: true,
      },
      ifShow: pageType === PageTypeEnum.VIEW,
    },
    {
      label: t('countryRegion'),
      field: 'countryName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: ' ',
        disabled: true,
      },
      ifShow: pageType === PageTypeEnum.VIEW,
    },
    {
      label: t('regionCountryToWhichItBelongs'),
      field: 'regionCountryCode',
      labelWidth: '100%',
      component: 'ApiTreeSelect',
      colProps: { span: 8 },
      required: true,
      componentProps: {
        placeholder: t('chooseText'),
        disabled,
        immediate: true,
        loadInVisible: true,
        api: () => {
          return getRegionNationTreeData();
        },
        showSearch: true,
      },
      ifShow: pageType !== PageTypeEnum.VIEW,
    },
    {
      label: t('provinceCode'),
      field: 'provinceLetterCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
    },
    {
      label: t('provinceName'),
      field: 'provinceCname',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
    },
  ];

  const submitButtonOptions = ref({ text: t('saveText'), preIcon: '', loading: false });

  const { close: closeTab } = useTabs();

  const [registerForm, { setFieldsValue }] = useForm({
    schemas: formSchemas,
    autoSubmitOnEnter: true,
    showResetButton: false,
    showCancelButton: true,
    cancelButtonOptions: { text: t(`${pageType === PageTypeEnum.VIEW ? 'back' : 'cancelText'}`), preIcon: '' },
    showSubmitButton: [PageTypeEnum.EDIT, PageTypeEnum.ADD].some((v) => v === pageType),
    submitButtonOptions,
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
  });

  /** 提交按钮配置 */
  const apiMap = {
    [PageTypeEnum.ADD]: featchAddProvince,
    [PageTypeEnum.EDIT]: featchUpdateProvince,
  };

  const dataDetail = ref({});

  const { createMessage } = useMessage();

  const handleSubmit = async (values: Record<string, any>) => {
    if (Reflect.ownKeys(values).length === 0) return;
    const api = apiMap[pageType];
    const params = values;
    if (pageType === PageTypeEnum.EDIT) {
      params.id = id;
    }
    if (params.regionCountryCode) {
      const [regionCode, countryCode] = params.regionCountryCode.split('-');
      params.countryCode = countryCode;
      params.regionCode = regionCode;
      delete params.regionCountryCode;
    }

    submitButtonOptions.value.loading = true;
    const [err] = await awaitTo(api(params));
    if (!err) {
      createMessage.success(t('operationSuccess'));
      handleCancel();
    }
    submitButtonOptions.value.loading = false;
  };

  const getDetail = async () => {
    const [, content] = await awaitTo(featchProvinceDetail({ id }));
    if (content) {
      dataDetail.value = content;
      const fieldsValue = Object.entries(dataDetail.value).reduce(
        (pre, [k, v]) => {
          if (isUndefined(v) || v === null) return pre;
          if (k === 'regionCode' && v) {
            pre['regionCountryCode'] = `${v}-${pre.countryCode}`;
            return pre;
          }
          pre[k] = v;
          return pre;
        },
        {} as Record<string, any>
      );
      setFieldsValue(fieldsValue);
    }
  };

  const handleCancel = () => {
    closeTab();
    router.push('/basic-data/geographical-location/state');
  };

  onMounted(async () => {
    if (pageType !== PageTypeEnum.ADD) {
      getDetail();
    }
  });
</script>

<style lang="less" scoped>
  ::v-deep(.ant-card-head) {
    border-bottom: none;
  }
</style>
