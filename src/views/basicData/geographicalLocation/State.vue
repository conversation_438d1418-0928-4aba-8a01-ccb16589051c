<template>
  <page-wrapper v-show="showCurrentRoute">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <TableAction :actions="renderTableTitleActions()" />
      </template>
      <template #action="{ record }">
        <TableAction :actions="renderTableActions(record)" />
      </template>
    </BasicTable>
    <BatchImportModal
      @register="registerModal"
      :title="modalTitle"
      :api="importStateData"
      @success="reload"
      @fail="reload"
      :templateFn="getTemplateFn"
    />
  </page-wrapper>
  <router-view />
</template>

<script lang="ts" setup>
  import { onActivated, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import { useSessionStorageState } from 'vue-hooks-plus';
  import { awaitTo } from '@ruqi/utils-admin/esm/tools';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useShowCurrentRouteByName } from '/@/hooks/route';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { useModal } from '/@/components/Modal';
  import BatchImportModal from '/@/components/BasicData/BatchImportModal.vue';
  import { BasicColumn, BasicTable, FormSchema, ActionItem, TableAction } from '/@/components/Table';
  import { AuthEnum } from '/@/enums/authEnum';
  import { PageTypeUnion } from '/@/enums/common';
  import { getObsTemporaryUrls, ObsTemporaryType } from '/@/api/obs';
  import { featchDeleteProvince, featchProvinceList, importStateData } from '/@/api/basicData/nationCity';
  import { downloadExcelFile } from '/@/utils/file/download';
  import { useLocale } from '/@/locales/useLocale';

  const t = useI18n('common').t;
  const router = useRouter();
  const { createConfirm, createMessage } = useMessage();
  const showCurrentRoute = useShowCurrentRouteByName('State');
  const { getLocale } = useLocale();
  const locale = unref(getLocale).replace('-', '_');

  const [, setItem] = useSessionStorageState('StateItem', {
    defaultValue: {},
  });

  const [registerModal, { openModal }] = useModal();
  const modalTitle = t('importStateData');

  const columns: BasicColumn[] = [
    {
      title: t('provinceCode'),
      dataIndex: 'provinceLetterCode',
      width: 150,
      resizable: true,
    },
    {
      title: t('provinceName'),
      dataIndex: 'provinceCname',
      width: 150,
      resizable: true,
    },
    {
      title: t('region'),
      dataIndex: 'regionName',
      width: 150,
      resizable: true,
    },
    {
      title: t('countryRegion'),
      dataIndex: 'countryName',
      width: 150,
      resizable: true,
    },
    {
      title: t('updateTime'),
      dataIndex: 'updateTime',
      width: 150,
      resizable: true,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      label: t('provinceCode'),
      field: 'provinceLetterCode',
      component: 'Input',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
      },
    },
    {
      label: t('provinceName'),
      field: 'provinceCname',
      component: 'Input',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
      },
    },
  ];

  const { tableContext, onExportXls } = useListPage({
    designScope: 'State',
    tableProps: {
      api: async (params) => {
        if (!showCurrentRoute) return;
        return featchProvinceList(params);
      },
      columns,
      formConfig: {
        schemas: searchFormSchema,
        buttonLayout: 'right',
        layout: 'vertical',
      },
      useSearchForm: true,
      showTableSetting: false,
      showActionColumn: true,
      actionColumn: {
        width: 150,
        title: t('operation'),
      },
    },
    exportConfig: {
      name: `provinceList_${locale}`,
      url: '/geography/province/export',
      methods: 'post',
    },
  });

  const [registerTable, { reload }] = tableContext;
  const handleRouteAction = (type: PageTypeUnion, record?: Recordable) => {
    setItem(record);
    router.push({ path: `/basic-data/geographical-location/state/${type}` });
  };

  const renderTableTitleActions = (): ActionItem[] => {
    return [
      {
        label: t('add'),
        size: 'middle',
        onClick: () => {
          handleRouteAction('add');
        },
        auth: AuthEnum.ADD_STATE,
        type: 'primary',
      },
      {
        label: t('export'),
        size: 'middle',
        onClick: async () => {
          onExportXls();
        },
        auth: AuthEnum.EXPORT_STATE,
        type: 'primary',
      },
      {
        label: t('import'),
        size: 'middle',
        onClick: async () => {
          openModal();
        },
        auth: AuthEnum.IMPORT_STATE,
        type: 'primary',
      },
    ];
  };

  const renderTableActions = (record: Recordable): ActionItem[] => {
    return [
      {
        label: t('view'),
        onClick: () => {
          handleRouteAction('view', record);
        },
        auth: AuthEnum.VIEW_STATE,
      },
      {
        label: t('edit'),
        onClick: () => {
          handleRouteAction('edit', record);
        },
        auth: AuthEnum.EDIT_STATE,
      },
      {
        label: t('delText'),
        color: 'error',
        onClick: () => {
          createConfirm({
            title: t('okTextDelete'),
            iconType: 'warning',
            content: `${t('deleteTip')}`,
            okText: t('okText'),
            cancelText: t('cancelText'),
            onOk: async () => {
              const [err] = await awaitTo(
                featchDeleteProvince({
                  id: record.id,
                })
              );
              if (!err) {
                createMessage.success(t('operationSuccess'));
                reload();
              }
            },
          });
        },
        auth: AuthEnum.DELETE_STATE,
      },
    ];
  };

  const getTemplateFn = async () => {
    const downloadFileName = `provinceImport_${locale}.xlsx`;
    const [err, result] = await awaitTo(
      getObsTemporaryUrls({
        type: ObsTemporaryType.Download,
        filePath: `public/${downloadFileName}`,
      })
    );
    if (err) {
      createMessage.error(t('file_download_fail'));
      return;
    }
    if (result.content) {
      try {
        downloadExcelFile(result.content, downloadFileName);
      } catch (err) {
        createMessage.error(t('file_download_fail'));
      }
    }
  };
  onActivated(async () => {
    reload && reload();
  });
</script>

<style lang="scss" scoped></style>
