import { Ref } from 'vue';
import { BasicColumn, FormSchema } from '/@/components/Table';
import { PageTypeEnum, PageTypeUnion } from '/@/enums/common';
import {
  DefaultSendFlagEnum,
  DefaultSendFlagOptions,
  JumpTypeEnum,
  JumpTypeOptions,
  MessageNotifyPeopleTypeEnum,
  MessageNotifyPeopleTypeOptions,
  TemplateTypeEnum,
} from '/@/enums/messageCenter';
import { SelectOptionType } from '/@/hooks/common/useRegionOptions';
import { useI18n } from '/@/hooks/web/useI18n';

export type UseBasePushTaskConfigOptions = {
  taskType: TemplateTypeEnum;
  pageType: PageTypeUnion;
  disabled: boolean;
  filterLanguageOptions: SelectOptionType[] | Ref<SelectOptionType[]>;
  validateChooseType: () => Promise<void>;
};

const useBasePushTaskConfig = (options: UseBasePushTaskConfigOptions) => {
  const { taskType, pageType, disabled, filterLanguageOptions, validateChooseType } = options;
  const { t } = useI18n('common');

  const formSchemas: FormSchema[] = [
    {
      label: '',
      field: 'basicInformation',
      slot: 'basicInformationTitle',
      component: 'Input',
    },
    {
      label: t('taskName'),
      field: 'taskName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
    },
    {
      label: '',
      field: 'messageContent',
      slot: 'messageContentTitle',
      component: 'Input',
    },
    {
      label: '',
      field: 'selectTemplate',
      slot: 'selectTemplate',
      component: 'Input',
    },
    {
      label: t('templateId'),
      field: 'templateId',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('language'),
      required: true,
      component: 'Select',
      field: 'language',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: filterLanguageOptions,
        disabled,
      },
    },
    {
      label: t('title'),
      required: true,
      component: 'Input',
      field: 'title',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        placeholder: '',
        disabled: true,
      },
      ifShow: taskType !== TemplateTypeEnum.SMS,
    },
    {
      component: 'RadioGroup',
      label: t('jumpType'),
      field: 'jumpType',
      required: false,
      colProps: { span: 24 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: t('chooseText'),
        options: JumpTypeOptions,
      },
      defaultValue: JumpTypeEnum.NO,
      ifShow: taskType === TemplateTypeEnum.PUSH,
    },
    {
      component: 'Input',
      label: t('jumpLink'),
      field: 'jumpLink',
      colProps: { span: 8 },
      required: false,
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: t('pleaseEnterContent'),
      },
      ifShow: taskType === TemplateTypeEnum.PUSH,
    },
    {
      field: 'content',
      component: taskType === TemplateTypeEnum.SMS ? 'InputTextArea' : 'JEditor',
      label: t('messageContent'),
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
        rows: 5,
        // JEditor 的属性
        showImageUpload: false,
        autoFocus: false,
      },
    },
    {
      label: '',
      field: 'taskCycle',
      slot: 'taskCycleTitle',
      component: 'Input',
    },
    {
      label: t('sendRegularly'),
      field: 'isScheduled',
      component: 'Select',
      colProps: { span: 8 },
      required: true,
      labelWidth: '100%',
      componentProps: {
        disabled,
        options: DefaultSendFlagOptions,
      },
      defaultValue: DefaultSendFlagEnum.NO,
    },
    {
      field: 'cronExpression',
      component: 'Input',
      slot: 'cronExpressionComponent',
      colProps: { span: 24 },
      label: '',
      labelWidth: 0,
      componentProps: {
        disabled,
      },
      ifShow: false,
    },
    // {
    //   field: 'cronExpression',
    //   component: 'JEasyCron',
    //   required: true,
    //   labelWidth: 150,
    //   colProps: { span: 12 },
    //   label: t('cronExpression'),
    //   componentProps: {
    //     disabled,
    //     placeholder: t('pleaseEnterContent'),
    //   },
    //   ifShow: false,
    // },
    {
      label: '',
      field: 'messageNotifyPeople',
      slot: 'messageNotifyPeopleTitle',
      component: 'Input',
    },
    {
      label: t('selectType'),
      field: 'selectType',
      component: 'RadioGroup',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        disabled,
        options: MessageNotifyPeopleTypeOptions,
      },
      ifShow: pageType === PageTypeEnum.ADD,
      defaultValue: MessageNotifyPeopleTypeEnum.MANUAL_UPLOAD,
    },
    {
      label: '',
      field: 'chooseType',
      slot: 'chooseTypeTitle',
      component: 'Input',
      rules: [
        {
          required: true,
          validator: validateChooseType,
        },
      ],
    },
    // {
    //   label: t('cronExpression'),
    //   field: 'cronExpressionComponent',
    //   slot: 'cronExpressionComponent',
    //   component: 'Input',
    // },
  ];

  const columns: BasicColumn[] = [
    {
      title: t('userId'),
      dataIndex: 'userId',
      width: 150,
      resizable: true,
    },
    {
      title: t('userName'),
      dataIndex: 'username',
      width: 150,
      resizable: true,
    },
    {
      title: t('mobile'),
      dataIndex: 'mobile',
      width: 150,
      resizable: true,
    },
    {
      title: t('email'),
      dataIndex: 'email',
      width: 150,
      resizable: true,
    },
  ];

  return {
    formSchemas,
    columns,
  };
};

export default useBasePushTaskConfig;
