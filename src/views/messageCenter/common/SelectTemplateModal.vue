<template>
  <BasicModal
    :showCancelBtn="true"
    :showOkBtn="true"
    :width="850"
    :max-height="700"
    destroyOnClose
    :ok-button-props="okButtonProps"
    :maximize="false"
    :mask-closable="false"
    :canFullscreen="false"
    @cancel="handleClose"
    @register="register"
    @ok="handleOk"
  >
    <BasicTable @register="registerTable" :rowSelection="rowSelection" />
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent, ref, unref, watch } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicColumn, FormSchema, BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { MessageTypeOptions, TemplateTypeEnum } from '/@/enums/messageCenter';
  import { getLabelFromDict } from '@ruqi/utils-admin';
  import { queryMessageTemplateList } from '/@/api/messageCenter/smsTemplate';
  import { EnableOrDisableStatusEnum } from '/@/enums/common';

  export default defineComponent({
    components: { BasicModal, BasicTable },
    props: {
      templateType: {
        type: Number as unknown as PropType<TemplateTypeEnum>,
        default: TemplateTypeEnum.SMS,
      },
      selectedRowKeys: {
        type: Array<string>,
        default: [],
      },
    },
    emits: ['register', 'close', 'confirm'],
    setup(props, { emit }) {
      const { t } = useI18n('common');

      const { templateType } = props;

      const [register, { closeModal }] = useModalInner(() => {});

      const { createMessage } = useMessage();

      // 弹窗的确认按钮状态
      const okButtonProps = ref({
        disabled: false,
      });

      const columns: BasicColumn[] = [
        {
          title: t('templateId'),
          dataIndex: 'templateId',
          width: 150,
          resizable: true,
        },
        {
          title: t('templateName'),
          dataIndex: 'templateName',
          width: 150,
          resizable: true,
        },
        {
          title: t('messageType'),
          dataIndex: 'messageType',
          width: 150,
          resizable: true,
          customRender: ({ record }) => getLabelFromDict(record.messageType, MessageTypeOptions),
        },
      ];

      const searchFormSchemas: FormSchema[] = [
        {
          label: t('messageType'),
          field: 'messageType',
          component: 'Select',
          labelWidth: 150,
          componentProps: {
            placeholder: t('chooseText'),
            options: MessageTypeOptions,
          },
        },
        {
          label: t('templateName'),
          field: 'templateName',
          component: 'Input',
          labelWidth: '150',
          componentProps: {
            placeholder: t('pleaseEnterContent'),
          },
        },
        {
          label: t('templateId'),
          field: 'templateId',
          component: 'Input',
          labelWidth: '100%',
          componentProps: {
            placeholder: t('pleaseEnterContent'),
          },
        },
      ];

      const { tableContext } = useListPage({
        tableProps: {
          rowKey: 'templateId',
          api: async (params) => {
            params.templateType = templateType;
            return queryMessageTemplateList({
              ...params,
              status: EnableOrDisableStatusEnum.ENABLE,
            });
          },
          maxHeight: 400,
          clickToRowSelect: true,
          columns,
          formConfig: {
            schemas: searchFormSchemas,
            buttonLayout: 'right',
            layout: 'vertical',
          },
          afterFetch: () => {
            okButtonProps.value.disabled = getRawDataSource()?.length === 0;
          },
          useSearchForm: true,
          showTableSetting: false,
          showActionColumn: false,
          rowSelection: {
            type: 'radio',
          },
        },
      });

      const [registerTable, { getRawDataSource }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;

      /** 关闭弹窗 */
      const handleClose = () => {
        closeModal();
        emit('close');
      };

      const handleOk = () => {
        if (unref(selectedRowKeys).length === 0) {
          return createMessage.error(t('chooseText'));
        }
        emit('confirm', selectedRows.value, selectedRowKeys.value);
        handleClose();
      };

      watch(
        () => props.selectedRowKeys,
        (val: string[]) => {
          selectedRowKeys.value = val ?? [];
        },
        {
          deep: true,
        }
      );

      return {
        register,
        handleClose,
        registerTable,
        rowSelection,
        selectedRows,
        handleOk,
        okButtonProps,
      };
    },
  });
</script>
