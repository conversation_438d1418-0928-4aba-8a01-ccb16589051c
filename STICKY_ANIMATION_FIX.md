# 筛选器吸顶动画修复

## 问题描述
用户反馈取消吸顶时没有触发动画效果，只有进入吸顶状态时有动画。

## 问题分析
1. **缺少退出动画**: 原来只处理了进入吸顶状态的动画，没有处理退出动画
2. **动画控制不完整**: 需要同时控制进入和退出两种状态的动画
3. **CSS动画类缺失**: 缺少退出动画的CSS关键帧定义

## 解决方案

### 1. 添加退出动画状态控制

```typescript
// 动画控制状态
const isAnimating = ref(false);  // 进入动画
const isExiting = ref(false);    // 退出动画
```

### 2. 完善动画状态监听

```typescript
watch(
  isSticky,
  (newIsSticky, oldIsSticky) => {
    emit('sticky-change', newIsSticky);

    // 控制动画
    if (newIsSticky) {
      // 进入吸顶状态
      isAnimating.value = true;
      isExiting.value = false;
      setTimeout(() => {
        isAnimating.value = false;
      }, 400);
    } else if (oldIsSticky !== undefined) {
      // 退出吸顶状态（避免初始化时触发）
      isExiting.value = true;
      isAnimating.value = false;
      setTimeout(() => {
        isExiting.value = false;
      }, 400);
    }
  },
  { immediate: true }
);
```

### 3. 更新模板类名绑定

```vue
<div
  ref="filterContentRef"
  class="filter-content"
  :class="{
    'is-sticky': isSticky,
    'sticky-transition': true,
    'sticky-enter': isSticky && isAnimating,
    'sticky-exit': !isSticky && isExiting,
  }"
  :style="stickyStyles"
>
```

### 4. 添加退出动画CSS

```css
&.sticky-exit {
  animation: stickyExit 0.4s ease-out;
}

@keyframes stickyExit {
  0% {
    transform: translateY(0);
    opacity: 1;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
  100% {
    transform: translateY(-10px);
    opacity: 0.8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}
```

## 动画效果说明

### 进入动画 (stickyEnter)
- **效果**: 从上方滑入，透明度从0.8到1，阴影从浅到深
- **时长**: 0.4秒
- **缓动**: ease-out

### 退出动画 (stickyExit)  
- **效果**: 向上方滑出，透明度从1到0.8，阴影从深到浅
- **时长**: 0.4秒
- **缓动**: ease-out

### 指示器动画
- **淡入滑动**: fadeInSlide 动画
- **脉冲效果**: pulse 动画（图标持续脉冲）

## 技术特点

1. **双向动画**: 支持进入和退出两个方向的动画
2. **状态控制**: 使用响应式变量精确控制动画时机
3. **避免冲突**: 通过状态管理避免动画冲突
4. **性能优化**: 使用CSS动画而非JS动画，性能更好
5. **调试友好**: 支持调试模式，可以查看动画状态变化

## 测试验证

现在可以测试以下场景：

1. **进入吸顶**: 滚动页面，筛选器应该有从上方滑入的动画
2. **退出吸顶**: 滚动回顶部，筛选器应该有向上方滑出的动画
3. **指示器动画**: 吸顶时指示器应该有淡入动画和脉冲效果
4. **响应式**: 在不同屏幕尺寸下动画应该正常工作

## 注意事项

- 动画时长设置为400ms，与CSS transition保持一致
- 使用`oldIsSticky !== undefined`避免组件初始化时触发退出动画
- 退出动画的变换距离较小(-10px)，避免过于突兀
- 保持了原有的所有功能，只是增强了动画效果
