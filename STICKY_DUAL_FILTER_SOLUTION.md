# 双筛选器吸顶方案

## 设计理念

采用双筛选器方案来解决吸顶动画问题：
- **原始筛选器**: 始终保持在原位，吸顶时变为半透明
- **吸顶筛选器**: 只在吸顶时显示的副本，带有完整的进入/退出动画

## 实现方案

### 1. 模板结构

```vue
<div class="sticky-filter-wrapper">
  <!-- 原始筛选器容器 - 始终存在 -->
  <div ref="filterContainerRef" class="filter-container">
    <div
      ref="filterContentRef"
      class="filter-content original-filter"
      :class="{ 'is-hidden': isSticky }"
    >
      <div class="filter-inner">
        <slot name="filter" :isSticky="false"></slot>
      </div>
    </div>
  </div>

  <!-- 吸顶筛选器副本 - 只在吸顶时显示 -->
  <Transition
    name="sticky-filter"
    @enter="onStickyEnter"
    @leave="onStickyLeave"
  >
    <div
      v-if="isSticky || isExiting"
      class="filter-content sticky-filter"
      :style="stickyStyles"
    >
      <div class="filter-inner">
        <slot name="filter" :isSticky="true"></slot>
      </div>
      
      <div v-if="showStickyIndicator" class="sticky-indicator">
        <Icon icon="ant-design:pin-filled" />
        <span>筛选器已固定</span>
      </div>
    </div>
  </Transition>
</div>
```

### 2. 样式设计

#### 原始筛选器样式
```css
.original-filter {
  transition: opacity 0.3s ease;

  &.is-hidden {
    opacity: 0.3;           // 吸顶时变为半透明
    pointer-events: none;   // 禁用交互
  }
}
```

#### 吸顶筛选器样式
```css
.sticky-filter {
  // 通过 stickyStyles 计算属性控制位置
  
  .filter-inner {
    padding: 16px 24px;     // 稍微紧凑的内边距
  }
}
```

#### Vue Transition 动画
```css
.sticky-filter-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.sticky-filter-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.sticky-filter-enter-from {
  transform: translateY(-20px);
  opacity: 0;
}

.sticky-filter-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}
```

### 3. 逻辑控制

#### 状态管理
```typescript
const isExiting = ref(false);

watch(isSticky, (newIsSticky, oldIsSticky) => {
  if (!newIsSticky && oldIsSticky !== undefined) {
    // 退出吸顶状态时，延迟隐藏以显示退出动画
    isExiting.value = true;
    setTimeout(() => {
      isExiting.value = false;
    }, 400);
  } else {
    isExiting.value = false;
  }
});
```

#### 显示条件
- **原始筛选器**: 始终显示，吸顶时变半透明
- **吸顶筛选器**: `v-if="isSticky || isExiting"`

## 优势分析

### 1. 无位置跳跃
- 原始筛选器始终在原位，不会产生布局跳跃
- 不需要占位符来维持高度

### 2. 流畅动画
- 进入动画：从上方滑入，透明度从0到1
- 退出动画：向上滑出，透明度从1到0
- 使用Vue Transition，动画更可控

### 3. 视觉连续性
- 原始筛选器变半透明，用户能感知到关联性
- 吸顶筛选器有明显的视觉区分（阴影、指示器）

### 4. 交互清晰
- 吸顶时原始筛选器禁用交互，避免混淆
- 吸顶筛选器有完整的交互功能

## 技术特点

1. **双实例管理**: 两个筛选器实例，分别处理不同状态
2. **Vue Transition**: 使用Vue内置过渡系统，更稳定
3. **状态隔离**: 原始和吸顶状态完全分离，无冲突
4. **动画可控**: 进入和退出动画都可以精确控制
5. **无占位符**: 不需要占位符维持布局，更简洁

## 用户体验

### 进入吸顶
1. 原始筛选器逐渐变半透明
2. 吸顶筛选器从上方滑入
3. 显示吸顶指示器

### 退出吸顶
1. 吸顶筛选器向上滑出并淡出
2. 原始筛选器恢复正常透明度
3. 无位置跳跃，过渡自然

## 兼容性

- 保持原有的所有API接口
- 支持所有原有的配置选项
- 向后兼容，无破坏性变更

## 性能考虑

- 两个筛选器实例会增加一定的内存占用
- 但避免了复杂的位置计算和占位符管理
- 动画性能更好，使用GPU加速的transform
