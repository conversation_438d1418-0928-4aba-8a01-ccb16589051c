# useFilters Hook 重复触发问题分析

## 🔍 问题现象

当触发吸顶效果时，`useFilters.ts` Hook 会再次触发，导致：
- 组件重新初始化
- API 可能重复调用
- 性能损耗
- 状态重置

## 🎯 根本原因

### 1. 双实例架构导致的问题

当前的双筛选器架构中：

```vue
<!-- 原始筛选器 -->
<div class="original-filter" :class="{ 'is-hidden': isSticky }">
  <slot name="filter" :isSticky="false"></slot>  <!-- FilterPanel 实例1 -->
</div>

<!-- 吸顶筛选器 -->
<div v-if="isSticky" class="sticky-filter">
  <slot name="filter" :isSticky="true"></slot>   <!-- FilterPanel 实例2 -->
</div>
```

### 2. Vue 组件重新创建

每次 `isSticky` 状态变化时：
- `v-if="isSticky"` 导致吸顶筛选器完全重新创建/销毁
- 每个 `FilterPanel` 实例都会调用 `useFilters()` Hook
- Hook 中的 `onMounted` 等生命周期重新执行

### 3. 具体触发时机

```typescript
// 主组件中
<template #filter="{ isSticky }">
  <FilterPanel
    ref="filterPanelRef"
    :class="isSticky ? 'sticky-mode' : ''"
    // 每次 isSticky 变化，这个组件都会重新渲染
  />
</template>
```

## 💡 解决方案

### 方案1: 使用 `v-show` 替代 `v-if` ✅

**优势**:
- 组件只创建一次，不会重复触发 Hook
- 只是控制显示/隐藏，不重新创建 DOM
- 性能更好

**实现**:
```vue
<!-- 修改前 -->
<div v-if="isSticky" class="sticky-filter">
  <slot name="filter" :isSticky="true"></slot>
</div>

<!-- 修改后 -->
<div v-show="isSticky" class="sticky-filter">
  <slot name="filter" :isSticky="true"></slot>
</div>
```

### 方案2: 单实例共享 (推荐) 🌟

**更彻底的解决方案**:
```vue
<template>
  <div class="sticky-filter-wrapper">
    <!-- 单一筛选器实例 -->
    <div
      ref="filterContentRef"
      class="filter-content"
      :class="{
        'is-sticky': isSticky,
        'original-position': !isSticky
      }"
      :style="isSticky ? stickyStyles : {}"
    >
      <div class="filter-inner">
        <!-- 只有一个 FilterPanel 实例 -->
        <slot name="filter" :isSticky="isSticky"></slot>
      </div>
    </div>
  </div>
</template>
```

### 方案3: Hook 缓存优化

在 `useFilters` 中添加缓存机制：
```typescript
// 使用 WeakMap 缓存 Hook 实例
const hookCache = new WeakMap();

export function useFilters(opt: { enableUserDataAuth: boolean }) {
  // 检查是否已有缓存实例
  if (hookCache.has(getCurrentInstance())) {
    return hookCache.get(getCurrentInstance());
  }
  
  // 创建新实例并缓存
  const hookInstance = createFiltersHook(opt);
  hookCache.set(getCurrentInstance(), hookInstance);
  
  return hookInstance;
}
```

## 🚀 推荐实现

我建议采用 **方案1 (v-show)** 作为快速修复，因为：

1. **最小改动**: 只需要改一行代码
2. **立即生效**: 解决重复触发问题
3. **向后兼容**: 不影响现有功能
4. **性能提升**: 避免组件重复创建

## 📊 性能对比

### 修改前 (v-if)
- ❌ 每次吸顶切换都重新创建组件
- ❌ useFilters Hook 重复执行
- ❌ 可能的 API 重复调用
- ❌ 状态重置

### 修改后 (v-show)
- ✅ 组件只创建一次
- ✅ Hook 只执行一次
- ✅ 状态保持连续
- ✅ 更好的性能

## 🔧 实施步骤

1. **立即修复**: 将 `v-if` 改为 `v-show`
2. **测试验证**: 确认不再重复触发
3. **性能监控**: 观察性能提升效果
4. **后续优化**: 考虑单实例方案

## 📝 注意事项

使用 `v-show` 后：
- 吸顶筛选器的 DOM 始终存在，只是隐藏
- CSS 动画可能需要调整
- 内存占用略有增加（但可忽略）

这个修改能够有效解决 `useFilters` 重复触发的问题，提升组件性能。
